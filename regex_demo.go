package main

import (
	"fmt"
	"regexp"
)

func main() {
	// 测试用例
	testCases := []string{
		"NGFW_NTOS 1.0R8P7S1, Release(05172822)", // 带逗号带空格
		"NGFW_NTOS 1.0R8P7S1,Release(05172822)",  // 带逗号不带空格
		"NGFW_NTOS 1.0R8P7S1 Release(05172822)",  // 不带逗号带空格
		"NGFW_NTOS 1.0R8P7S1Release(05172822)",   // 不带逗号不带空格
		"NGFW_NTOS 1.0R8P7S1",                    // 没有Release部分
	}

	// 原始正则表达式
	oldRegex := regexp.MustCompile(`,Release\(.*\)`)

	// 新的正则表达式
	newRegex := regexp.MustCompile(`[,\s]+Release\(.*\)`)

	// 另一种新的正则表达式，处理没有逗号和空格的情况
	newRegex2 := regexp.MustCompile(`[,\s]*Release\(.*\)`)

	fmt.Println("测试软件版本处理正则表达式:")
	fmt.Println("===================================")

	for _, version := range testCases {
		fmt.Printf("原始版本: %s\n", version)

		// 使用原始正则表达式处理
		oldResult := oldRegex.ReplaceAllString(version, "")
		fmt.Printf("旧正则处理结果: %s\n", oldResult)

		// 使用新的正则表达式处理
		newResult := newRegex.ReplaceAllString(version, "")
		fmt.Printf("新正则处理结果: %s\n", newResult)

		// 使用另一种新的正则表达式处理
		newResult2 := newRegex2.ReplaceAllString(version, "")
		fmt.Printf("新正则2处理结果: %s\n", newResult2)

		fmt.Println("-----------------------------------")
	}
}
