debug: false
loglevel: error # 日志级别 info debug disable error fatal warn 等等
host: 0.0.0.0 # host 地址
port: 9001
ssh:
  host: ***********
  user: regress
  password: 2022MaRtInI
nginx:
  host: localhost
  port: 9527
  path: /api
readtimeout: 600
writetimeout: 600
maxsize: 10240 # 文件上传限制
pprof: true #  http://localhost:8086/debug/pprof/heap ...
casbin:
  prefix:
  # path: /data/goprojects/irisAdminApi/rbac_model.conf # go run main.go 运行必须指定路径
  path: /projects/iris-admin-api/rbac_model.conf
cache:
  driver: redis # 缓存驱动 local redis
limit:
  disable: false
  limit: 20 # 每秒允许请求 1 次
  burst: 100 # 最高允许并发
admin: # 管理员账号信息，用于数据填充
  username: admin
  rolename: 超级管理员
  name: 超级管理员
  password: abcd@1234
db:
  adapter: mysql # mysql postgres sqlite3
  conn: "root:abc.123@tcp(127.0.0.1:3306)/iris?parseTime=True&loc=Local"
redis:
 host: 127.0.0.1
 port: 6379
 password: fileOut@2021!
qiniu:
  enable: false
  host:
  accesskey:
  secretkey:
  bucket:
filestorage:
  # 临时以及最后存在路径win/linux均以/作为分隔符，以/为结尾
  temp: /tmp/temp/ # win/linux均以/作为分隔符，以/为结尾
  upload: /tmp/upload/
  # objdump:
  # #   enable: true
  #   path: /usr/bin/objdump
  # filecommand:
  #   path: /usr/bin/file
  # rsa:
  # publickey: /home/<USER>/.ssh/id_rsa.pub
  # privatekey: /home/<USER>/.ssh/id_rsa

gitlab:
  url: http://*************:8180
  version: v4
  token: Et6H2_91RVZcsZM7pyKc
  aitoken: Et6H2_91RVZcsZM7pyKc

buildfarm:
  cronmailrecivers: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
  logpath: /tmp/  #编译日志路径
  compilepath: /tmp/  #编译项目路径
  archivepath: /tmp/  #编译输出归档路径
  temp: /tmp/
  user: buildfarm
  # token: TiYcqp7Lj7A6qnJ4uuwa
  token: SAEwzuvybVP7dffsnGHx
  enable: false
  jenkinsurl: http://**************:8080
  jenkinsuser: admin
  jenkinspass: admin

release:
  releasepath: /tmp/
  host: http://**************:8080/release

featurerelease:
  upload: /tmp/
  url: https://*************
  enable: false

checkccode:
  script: /home/<USER>/doc2vec/check.py
  repository: /home/<USER>/repositorys/

mail:
  enable: true
  queue: mail

patchfilestorage:
  temp: /tmp/temp/ # 临时目录
  upload: /tmp/upload/patch/ #上传保存目录
  compilepath: /tmp/compile/  #编译项目路径
  archivepath: /tmp/compileoutput/  #编译输出路径
  gitrepo: ssh://git@**************:8022/daimingwei/patch_build.git #工具仓库地址
  branch: master #仓库分支

sync:
  enable: false

codesync:
  token: SAEwzuvybVP7dffsnGHx
  enable: false

coredump:
  upload: /tmp/coredump
  output: /tmp/coredump/output
  host: 127.0.0.1
  port: 9022
  username: root
  plugin: /projects/iris-admin-api/plugins
  remoteworkdir: /mnt/sata0

urlpack:
  outputs: /tmp/urlpack/outputs
  logs: /tmp/urlpack/logs
  enable: false

opensource:
  enable: false
  cvesynctaskworkers: 1
  #sendemailtaskworkers: 3
  #tempchecktaskworkers: 4
  #cvesyncintervalinhours: 169
  #vulnerabilitydelayedtimeinhours: 167
  #cnvdsyncintervalinhours: 166

license:
  enable: false
  #deviceauthtaskworkers: 2
  #adminusername: admin_auto_authorization
  #adminpassword: U0WlP2tXWkinYw+Ecc2+vg==
  #adminendpoint: https://**************
  #seccloudendpoint: https://secloud0.ruijie.com.cn
  #productionendpoint: http://************:4011
  #authcodeendpoint: https://**************
  #username: automation
  #password: uvgyhE0wcT4ZvmEwOPlqAg==
  #basicauthusername: security
  #basicauthpassword: Ruijie@123456

resourcepool:
  ssh:
    host: ***********
    user: regress
    password: 2022MaRtInI

mergerequest:
  enable: true
  opensource:
    host: 127.0.0.1
    port: 22
    username: linjiakai
    password: 2Ghlmcl!
  codecheckenable: false

sig:
  upload: /tmp/sig/
  sigpath: /opt/sig/
  techsupportdecryptpath: /opt/rg_cpkg/
  dpdinfopath: /opt/rg_cpkg/

datasync:
  svn:
    username: linjiakai
    password: 2Ghlmcl!
    outputPath: /tmp
  influxdb:
    url: http://************:9090/influxdb
    token: dXG9jj6ODGhMCjhEKsJwLBXtvzj_uqQwaAWkjgBlu6hndQQmzvLCiaJvJliQK3mMQ-3jMWKomgQNvJGhP9iGoA==
    org: aqyfzx
    bucket: gcov
  pmsuser: linjiakai
  pmspass: 2Ghlmcl!

feishudoc:
  enable: false
  appid: cli_a6864542233d900e
  appsecret: f9FupAX6aw65B6FLgyvNXbEywHq7keJF
  foldertoken: LSfAfsbqGlaC6Rd0wttc8yLynRd # 目录token
  biapptoken: KZQCbsWefa5e3MsamMccOYYPnMr # 多维表格应用token

coveritysync:
   enable: true
   url: http://************:9090/output/
   epgrulefilename: rule.json