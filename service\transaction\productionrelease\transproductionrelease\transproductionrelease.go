package transproductionrelease

import (
	"errors"
	"fmt"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/logging"
	"irisAdminApi/service/dao/productionrelease/dproduction"
	"irisAdminApi/service/dao/productionrelease/dproductionprocdef"
	"irisAdminApi/service/dao/productionrelease/dproductionprocinst"
	"irisAdminApi/service/dao/productionrelease/dproductionproctask"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskextension"
	"irisAdminApi/service/dao/productionrelease/dproductionproctaskhost"
	"irisAdminApi/service/dao/user/duser"
	"time"

	"gorm.io/gorm"
)

func CreateProductionTransaction(userID uint, featureObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()

	err := db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		resource := featureObject["Resource"].(string)
		delete(featureObject, "Resource")
		if err := tx.Model(dproduction.Model()).Create(featureObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		production := dproduction.Response{}
		if err := tx.Model(dproduction.Model()).Where("uuid = ?", featureObject["Uuid"]).Find(&production).Error; err != nil {
			return err
		}
		if production.ID == 0 {
			return errors.New("创建下生产发布流程失败")
		}
		procInstObject := map[string]interface{}{
			"Title": production.MainProgramFileName,
			// 当前节点
			"NodeID":       "start",
			"TaskID":       0,
			"StartUserID":  userID,
			"ProductionID": production.ID,
			"CreatedAt":    time.Now(),
			"UpdatedAt":    time.Now(),
			"Resource":     resource,
			"Status":       0,
		}
		if err := tx.Model(dproductionprocinst.Model()).Create(procInstObject).Error; err != nil {
			// 返回任何错误都会回滚事务
			return err
		}
		procInst := dproductionprocinst.Response{}
		if err := tx.Model(dproductionprocinst.Model()).Where("production_id = ?", production.ID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("创建生产发布实例失败")
		}
		procTaskObjects := []map[string]interface{}{}
		defResource := procInst.Resource
		nodes, _ := dproductionprocdef.GetNodes(defResource)

		// 创建 start 节点的任务
		for _, node := range nodes {
			if node.NodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   userID,
					"Status":     1,
					"Flag":       true,
					"Done":       true,
				})
				break
			}
		}

		// 创建 start 节点的下一个节点的任务
		for _, node := range nodes {
			assignee := node.Assignee
			if node.Assignee == 0 {
				assignee = userID
			}
			if node.PrevNodeID == "start" {
				procTaskObjects = append(procTaskObjects, map[string]interface{}{
					"CreatedAt":  time.Now(),
					"UpdatedAt":  time.Now(),
					"NodeName":   node.Name,
					"NodeID":     node.NodeID,
					"PrevNodeID": node.PrevNodeID,
					"ProcInstID": procInst.ID,
					"Assignee":   assignee,
					"Status":     0,
					"Flag":       true,
				})
			}
		}

		// 处理任务对象
		if len(procTaskObjects) > 0 {
			//判断新建节点类型
			// fmt.Print(procTaskObjects)
			todoTask := procTaskObjects
			//提取回滚节点
			rollBackIDs := []string{}
			for _, task := range procTaskObjects {
				rollBackIDs = append(rollBackIDs, task["NodeID"].(string))
			}
			//查找是否存在回滚节点
			rollBackTasks := []*dproductionproctask.Response{}
			if err := tx.Model(dproductionproctask.Model()).Where("node_id in ?  and proc_inst_id = ? and status = 0 and flag = 0 ", rollBackIDs, procInst.ID).Find(&rollBackTasks).Error; err != nil {
				return err
			}
			//存在的情况下,重置相关任务信息，flag置为true
			if len(rollBackTasks) > 0 {
				if err := tx.Model(dproductionproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = 0", rollBackIDs, procInst.ID).
					UpdateColumns(map[string]interface{}{"status": 0, "flag": true}).Error; err != nil {
					return err
				}
				// 移除回滚节点
				for _, task := range rollBackTasks {
					procTaskObjects = RemoveNodeID(procTaskObjects, task.NodeID)
				}
			} else {
				//不存在的情况下,判断是否为迭代发布，如果是，则需要获取最新上次版本的数据，获取相同节点的任务数据，补充填充相关内容platform_mg，chief_tester,cpld_check
				if production.ProductionType != "1" {
					productionCheck := dproduction.Response{}
					if err := tx.Model(dproduction.Model()).Where("id = ?", production.ProductionBaseVersion).Find(&productionCheck).Error; err != nil {
						return err
					}
					//需要获取最新上次版本的数据实例
					procinstCheck := dproductionprocinst.Response{}
					if err := tx.Model(dproductionprocinst.Model()).Where("production_id = ?", productionCheck.ID).Find(&procinstCheck).Error; err != nil {
						return err
					}
					//查找相同节点的任务数据 platform_mg，chief_tester,cpld_check
					for _, rollBackID := range rollBackIDs {
						// 对于 pm_submit 节点，我们不需要从上一个版本复制数据，因为它是新添加的节点
						if rollBackID == "pm_submit" {
							continue
						}

						if rollBackID == "platform_mg" {
							lastPlatformTask := dproductionproctask.Response{}
							if err := tx.Model(dproductionproctask.Model()).Last(&lastPlatformTask, "node_id ='platform_mg' and proc_inst_id =? and status = 1 and flag = true", procinstCheck.ID).Error; err != nil {
								return err
							}
							tempObject := FindByNodeID(procTaskObjects, rollBackID)
							if tempObject == nil {
								continue
							}
							// 移除节点信息
							procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
							//构造节点数据
							taskObject := map[string]interface{}{
								"CreatedAt":     time.Now(),
								"NodeName":      tempObject["NodeName"],
								"NodeID":        tempObject["NodeID"],
								"PrevNodeID":    tempObject["PrevNodeID"],
								"ProcInstID":    tempObject["ProcInstID"],
								"Assignee":      tempObject["Assignee"],
								"Status":        0,
								"Flag":          true,
								"Uboot":         lastPlatformTask.Uboot,
								"UbootMd5":      lastPlatformTask.UbootMd5,
								"UbootVersion":  lastPlatformTask.UbootVersion,
								"UbootDoc":      lastPlatformTask.UbootDoc,
								"SetmacTool":    lastPlatformTask.SetmacTool,
								"SetmacToolMd5": lastPlatformTask.SetmacToolMd5,
								"SetmacDoc":     lastPlatformTask.SetmacDoc,
								"SetmacIni":     lastPlatformTask.SetmacIni,
								"SetmacIniMd5":  lastPlatformTask.SetmacIniMd5,
								"RomProgram":    lastPlatformTask.RomProgram,
								"RomProgramMd5": lastPlatformTask.RomProgramMd5,
								"Done":          true,
							}
							if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
								return err
							}
							//查看是否存在SetmacHosts主机
							lastPlatformHosts := []*dproductionproctaskhost.Response{}
							if err := tx.Model(dproductionproctaskhost.Model()).Where("proc_inst_id =? ", procinstCheck.ID).Find(&lastPlatformHosts).Error; err != nil {
								return err
							}
							if len(lastPlatformHosts) > 0 {
								curPlatformTask := dproductionproctask.Response{}
								if err := tx.Model(dproductionproctask.Model()).Where("node_id =? and proc_inst_id =? and status = 0 and flag = 1", tempObject["NodeID"], procInst.ID).
									Order(fmt.Sprintf("%s %s", "created_at", "desc")).Find(&curPlatformTask).Error; err != nil {
									return err
								}
								if curPlatformTask.ID == 0 {
									return errors.New("获取节点数据失败")
								}
								//存在主机，则需要更新
								setmacHostObjects := []map[string]interface{}{}
								for _, host := range lastPlatformHosts {
									setmacHostObjects = append(setmacHostObjects, map[string]interface{}{
										"CreatedAt":     time.Now(),
										"TaskID":        curPlatformTask.ID,
										"ExtensionName": host.ExtensionName,
										"ExtensionTool": host.ExtensionTool,
										"MD5":           host.MD5,
										"ProcInstID":    procInst.ID,
									})
								}
								if err := tx.Model(dproductionproctaskhost.Model()).Create(setmacHostObjects).Error; err != nil {
									return err
								}
							}
							//查看是否存在Setmac扩展项
							lastPlatformExtensions := []*dproductionproctaskextension.Response{}
							if err := tx.Model(dproductionproctaskextension.Model()).Where("proc_inst_id =? ", procinstCheck.ID).Find(&lastPlatformExtensions).Error; err != nil {
								return err
							}
							if len(lastPlatformExtensions) > 0 {
								curPlatformTask := dproductionproctask.Response{}
								if err := tx.Model(dproductionproctask.Model()).Where("node_id =? and proc_inst_id =? and status = 0 and flag = 1", tempObject["NodeID"], procInst.ID).
									Order(fmt.Sprintf("%s %s", "created_at", "desc")).Find(&curPlatformTask).Error; err != nil {
									return err
								}
								if curPlatformTask.ID == 0 {
									return errors.New("获取节点数据失败")
								}
								//存在扩展项，则需要更新
								setmacObjects := []map[string]interface{}{}
								for _, extension := range lastPlatformExtensions {
									setmacObjects = append(setmacObjects, map[string]interface{}{
										"CreatedAt":     time.Now(),
										"TaskID":        curPlatformTask.ID,
										"ExtensionName": extension.ExtensionName,
										"ExtensionTool": extension.ExtensionTool,
										"MD5":           extension.MD5,
										"ExtensionDoc":  extension.ExtensionDoc,
										"ProcInstID":    procInst.ID,
									})
								}
								if err := tx.Model(dproductionproctaskextension.Model()).Create(setmacObjects).Error; err != nil {
									return err
								}
							}
						}
						if rollBackID == "chief_tester" {
							lastChiefTask := dproductionproctask.Response{}
							if err := tx.Model(dproductionproctask.Model()).Last(&lastChiefTask, "node_id ='chief_tester' and proc_inst_id =? and status = 1 and flag = true", procinstCheck.ID).Error; err != nil {
								return err
							}
							tempObject := FindByNodeID(procTaskObjects, rollBackID)
							if tempObject == nil {
								continue
							}
							// 移除节点信息
							procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
							//构造节点数据
							taskObject := map[string]interface{}{
								"CreatedAt":           time.Now(),
								"NodeName":            tempObject["NodeName"],
								"NodeID":              tempObject["NodeID"],
								"PrevNodeID":          tempObject["PrevNodeID"],
								"ProcInstID":          tempObject["ProcInstID"],
								"Assignee":            tempObject["Assignee"],
								"Status":              0,
								"Flag":                true,
								"ManufTestProgram":    lastChiefTask.ManufTestProgram,
								"ManufTestProgramDoc": lastChiefTask.ManufTestProgramDoc,
								"ManufTestReport":     lastChiefTask.ManufTestReport,
								"ManufTestProgramMD5": lastChiefTask.ManufTestProgramMD5,
								"Done":                true,
							}
							if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
								return err
							}
						}

						if rollBackID == "cpld_check" {
							lastCpldTask := dproductionproctask.Response{}
							if err := tx.Model(dproductionproctask.Model()).Last(&lastCpldTask, "node_id ='cpld_check' and proc_inst_id =? and status = 1 and flag = true", procinstCheck.ID).Error; err != nil {
								return err
							}
							tempObject := FindByNodeID(procTaskObjects, rollBackID)
							if tempObject == nil {
								continue
							}
							// 移除节点信息
							procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
							//构造节点数据
							taskObject := map[string]interface{}{
								"CreatedAt":     time.Now(),
								"NodeName":      tempObject["NodeName"],
								"NodeID":        tempObject["NodeID"],
								"PrevNodeID":    tempObject["PrevNodeID"],
								"ProcInstID":    tempObject["ProcInstID"],
								"Assignee":      tempObject["Assignee"],
								"Status":        0,
								"Flag":          true,
								"CpldUpdate":    lastCpldTask.CpldUpdate,
								"CpldUpdateUrl": lastCpldTask.CpldUpdateUrl,
								"Done":          true,
							}
							if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
								return err
							}
						}
					}
				}
			}
			if err := tx.Model(dproductionproctask.Model()).Create(procTaskObjects).Error; err != nil {
				return err
			}
			// 发送邮件
			go SendMail(todoTask, production.ID, production.Urgency)
		}
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateFeatureTransaction(userID, procInstID, taskID uint, featureObject map[string]interface{}, taskObject map[string]interface{}) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 查检当前处理节点是否重复处理
		task := dproductionproctask.Response{}
		if err := tx.Model(dproductionproctask.Model()).Where("id = ? and status = 0 and flag = true", taskID).Find(&task).Error; err != nil {
			return err
		}

		if task.ID == 0 {
			return errors.New("已处理任务，无法重复处理")
		}
		if task.Assignee != userID {
			return errors.New("不是当前用户的任务")
		}
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		procInst := dproductionprocinst.Response{}
		if err := tx.Model(dproductionprocinst.Model()).Where("id = ?", procInstID).Find(&procInst).Error; err != nil {
			return err
		}
		if procInst.ID == 0 {
			return errors.New("未找到下生产发布实例")
		}

		production := dproduction.Response{}
		if err := tx.Model(dproduction.Model()).Where("id = ?", procInst.ProductionID).Find(&production).Error; err != nil {
			return err
		}
		if production.ID == 0 {
			return errors.New("未找到下生产")
		}

		resource := procInst.Resource
		nodes, _ := dproductionprocdef.GetNodes(resource)
		procTaskObjects := []map[string]interface{}{}
		levelNodeIDs := []string{}
		prevTasks := []*dproductionproctask.Response{}
		levelTasks := []*dproductionproctask.Response{}
		nextTasks := []*dproductionproctask.Response{}

		prevNodeIDs, _ := dproductionprocdef.GetPrevNodeIDs(resource, taskObject["NodeID"].(string))
		nextNodeIDs, _ := dproductionprocdef.GetNextNodeIDs(resource, taskObject["NodeID"].(string))
		// _levelNodeIDs, _ := dproductionprocdef.GetNextNodeIDs(resource, task.PrevNodeID)

		//通过nextNodeIDs的前置节点寻找同级任务节点
		for _, nextNodeID := range nextNodeIDs {
			nextNodePrevNodeIDs, _ := dproductionprocdef.GetPrevNodeIDs(resource, nextNodeID)
			for _, nextNodePrevNodeID := range nextNodePrevNodeIDs {
				// 同级任务去掉当前任务节点id
				if nextNodePrevNodeID != task.NodeID {
					levelNodeIDs = append(levelNodeIDs, nextNodePrevNodeID)
				}
			}
		}
		// fmt.Println(taskObject["NodeID"].(string), task.NodeID, prevNodeIDs, nextNodeIDs, _levelNodeIDs, levelNodeIDs)
		switch taskObject["Status"].(uint) {
		case 1:
			//检查是否为驳回订单，如果是驳回订单，将后续节点flag 置为false
			taskObject["Done"] = true

			// PM 节点处理逻辑
			if task.NodeID == "pm_submit" {
				// 如果是 PM 节点，需要将 PM 节点的相关信息更新到 production 表中
				// 从 taskObject 中获取 PM 节点提交的程序信息
				updateFields := map[string]interface{}{}

				// 检查并更新各字段
				if mainProgramUrl, ok := taskObject["MainProgramUrl"]; ok {
					updateFields["MainProgramUrl"] = mainProgramUrl
				}
				if mainProgramFileName, ok := taskObject["MainProgramFileName"]; ok {
					updateFields["MainProgramFileName"] = mainProgramFileName
				}
				if mainProgramFileSize, ok := taskObject["MainProgramFileSize"]; ok {
					updateFields["MainProgramFileSize"] = mainProgramFileSize
				}
				if mainProgramFileMd5, ok := taskObject["MainProgramFileMd5"]; ok {
					updateFields["MainProgramFileMd5"] = mainProgramFileMd5
				}
				if ospkgInstallUrl, ok := taskObject["OspkgInstallUrl"]; ok {
					updateFields["OspkgInstallUrl"] = ospkgInstallUrl
				}
				if ospkgInstallName, ok := taskObject["OspkgInstallName"]; ok {
					updateFields["OspkgInstallName"] = ospkgInstallName
				}
				if ospkgInstallSize, ok := taskObject["OspkgInstallSize"]; ok {
					updateFields["OspkgInstallSize"] = ospkgInstallSize
				}
				if ospkgInstallMd5, ok := taskObject["OspkgInstallMd5"]; ok {
					updateFields["OspkgInstallMd5"] = ospkgInstallMd5
				}
				if softwareNumber, ok := taskObject["SoftwareNumber"]; ok {
					updateFields["SoftwareNumber"] = softwareNumber
				}
				if softwareVersion, ok := taskObject["SoftwareVersion"]; ok {
					updateFields["SoftwareVersion"] = softwareVersion
				}

				// 更新 production 表
				if len(updateFields) > 0 {
					if err := tx.Model(dproduction.Model()).Where("id = ?", procInst.ProductionID).UpdateColumns(updateFields).Error; err != nil {
						return err
					}
				}

				// 移除对 procInst 表的更新，因为 procInst 表中不包含这些字段
				// 只更新 procInst 表中的 Title 字段，如果 MainProgramFileName 存在
				if mainProgramFileName, ok := taskObject["MainProgramFileName"]; ok {
					if err := tx.Model(dproductionprocinst.Model()).Where("id = ?", procInst.ID).UpdateColumn("title", mainProgramFileName).Error; err != nil {
						return err
					}
				}
			}

			// Start 节点处理逻辑
			if task.NodeID == "start" {
				// 如果是 Start 节点，需要将 Start 节点的相关信息更新到 production 表中
				updateFields := map[string]interface{}{}

				// 检查并更新各字段
				if versionDesc, ok := featureObject["VersionDesc"]; ok {
					updateFields["VersionDesc"] = versionDesc
				}
				if versionType, ok := featureObject["VersionType"]; ok {
					updateFields["VersionType"] = versionType
				}
				if regionType, ok := featureObject["RegionType"]; ok {
					updateFields["RegionType"] = regionType
				}
				if productModel, ok := featureObject["ProductModel"]; ok {
					updateFields["ProductModel"] = productModel
				}
				if releaseDate, ok := featureObject["ReleaseDate"]; ok {
					updateFields["ReleaseDate"] = releaseDate
				}
				if urgency, ok := featureObject["Urgency"]; ok {
					updateFields["Urgency"] = urgency
				}

				// 更新 production 表
				if len(updateFields) > 0 {
					if err := tx.Model(dproduction.Model()).Where("id = ?", procInst.ProductionID).UpdateColumns(updateFields).Error; err != nil {
						return err
					}
				}
			}

			// 检查前置任务是否都通过
			if len(prevNodeIDs) > 0 {
				if err := tx.Model(dproductionproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", prevNodeIDs, procInst.ID).Find(&prevTasks).Error; err != nil {
					return err
				}
				if len(prevTasks) < len(prevNodeIDs) {
					return errors.New("前置节点未全部通过")
				}
			}

			// 检查同级任务是否都通过
			if err := tx.Model(dproductionproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 1 and flag = true", levelNodeIDs, procInst.ID).Find(&levelTasks).Error; err != nil {
				return err
			}
			// fmt.Println(levelTasks, levelNodeIDs, len(levelNodeIDs), len(levelTasks))

			if len(levelNodeIDs) <= len(levelTasks) {
				if len(nextNodeIDs) > 0 {
					if err := tx.Model(dproductionproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = true", nextNodeIDs, procInst.ID).Find(&nextTasks).Error; err != nil {
						return err
					}
				out:
					for _, nextNodeID := range nextNodeIDs {
						for _, nextTask := range nextTasks {
							if nextTask.NodeID == nextNodeID {
								continue out
							}
						}
						// 获取
						nextNodes, _ := dproductionprocdef.GetNodesByNodeID(procInst.Resource, nextNodeID)
						for _, node := range nextNodes {
							procTaskObjects = append(procTaskObjects, map[string]interface{}{
								"CreatedAt":  time.Now(),
								"NodeName":   node.Name,
								"NodeID":     node.NodeID,
								"PrevNodeID": node.PrevNodeID,
								"ProcInstID": procInst.ID,
								"Assignee":   node.Assignee,
								"Status":     0,
								"Flag":       true,
							})
						}
					}

				} else {
					featureObject["Status"] = 1
				}
			}
		case 2:
			taskObject["Flag"] = false
			prevTask := dproductionproctask.Response{}
			if err := tx.Model(dproductionproctask.Model()).Last(&prevTask, "node_id = ? and proc_inst_id = ? and status != 0 and flag = true", taskObject["NextNodeID"].(string), procInst.ID).Error; err != nil {
				return err
			}

			// 处理打回到pm_submit节点的情况
			if taskObject["NextNodeID"].(string) == "pm_submit" {
				// 清除相关数据，避免下次提交时检查到相同文件发布记录
				updateFields := map[string]interface{}{
					"MainProgramUrl":      "",
					"MainProgramFileName": "",
					"MainProgramFileSize": 0,
					"MainProgramFileMd5":  "",
					"OspkgInstallUrl":     "",
					"OspkgInstallName":    "",
					"OspkgInstallSize":    0,
					"OspkgInstallMd5":     "",
					"SoftwareNumber":      "",
					"SoftwareVersion":     "",
				}

				// 更新production表
				if err := tx.Model(dproduction.Model()).Where("id = ?", procInst.ProductionID).UpdateColumns(updateFields).Error; err != nil {
					return err
				}

				logging.DebugLogger.Debugf("已清除文件发布记录数据，流程被打回到 pm_submit 节点")
			}

			// 处理打回到start节点的情况
			if taskObject["NextNodeID"].(string) == "start" {
				// 清除相关数据，避免下次提交时检查到相同文件发布记录
				updateFields := map[string]interface{}{
					"MainProgramUrl":      "",
					"MainProgramFileName": "",
					"MainProgramFileSize": 0,
					"MainProgramFileMd5":  "",
					"OspkgInstallUrl":     "",
					"OspkgInstallName":    "",
					"OspkgInstallSize":    0,
					"OspkgInstallMd5":     "",
					"SoftwareNumber":      "",
					"SoftwareVersion":     "",
				}

				// 更新production表
				if err := tx.Model(dproduction.Model()).Where("id = ?", procInst.ProductionID).UpdateColumns(updateFields).Error; err != nil {
					return err
				}

				logging.DebugLogger.Debugf("已清除文件发布记录数据，流程被打回到 start 节点")
			}

			// 处理回退节点之后的未处理任务
			handleIDs := []string{}
			handleNodes, _ := dproductionprocdef.GetAfterNodes(nodes, taskObject["NextNodeID"].(string))
			for _, node := range handleNodes {
				handleIDs = append(handleIDs, node.NodeID)
			}
			handleIDs = append(handleIDs, taskObject["NodeID"].(string))
			handleIDs = append(handleIDs, taskObject["NextNodeID"].(string))
			handleIDs = UniqueStrings(handleIDs)
			if err := tx.Delete(dproductionproctask.Model(), "node_id = ? and proc_inst_id = ? and status = 1 and flag = true and id != ?", prevTask.NodeID, procInst.ID, taskID).Error; err != nil {
				return err
			}
			// fmt.Println(handleIDs)
			if err := tx.Model(dproductionproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status NOT IN (0, 3) and flag = true", handleIDs, procInst.ID).UpdateColumns(map[string]interface{}{"status": 0, "flag": false}).Error; err != nil {
				return err
			}

			//生成新的任务
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   prevTask.NodeName,
				"NodeID":     prevTask.NodeID,
				"PrevNodeID": prevTask.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   prevTask.Assignee,
				"Status":     0,
				"Flag":       true,
			})

			// 找出回退节点的所有同级节点（具有相同前置节点的节点）
			parallelNodes := []*dproductionprocdef.Node{}
			rollbackNodePrevID := prevTask.PrevNodeID
			for _, node := range nodes {
				// 找到所有和回退节点有相同前置节点的其他节点（并行节点）
				if node.PrevNodeID == rollbackNodePrevID && node.NodeID != prevTask.NodeID {
					parallelNodes = append(parallelNodes, node)
				}
			}

			// 判断发起回退的节点是否与目标回退节点属于同级（具有相同的前置节点）
			isParallelRollback := false
			rollbackSourceNodeID := taskObject["NodeID"].(string)

			// 查找发起回退的节点信息
			var rollbackSourceNode *dproductionprocdef.Node
			for _, node := range nodes {
				if node.NodeID == rollbackSourceNodeID {
					rollbackSourceNode = node
					break
				}
			}

			// 检查发起回退的节点与目标回退节点是否为同级节点
			if rollbackSourceNode != nil && rollbackSourceNode.PrevNodeID == rollbackNodePrevID {
				isParallelRollback = true
			}

			// 为所有并行节点创建新任务
			for _, parallelNode := range parallelNodes {
				// 回退场景下，需要判断以下情况:
				// 1. 如果节点已有有效任务(flag=true)且已完成(status=1)，不创建新任务
				// 2. 如果节点有待处理任务(status=0)，不创建新任务
				// 3. 其他情况，创建新任务

				// 检查是否有已完成(status=1)且有效(flag=true)的任务，或者有待处理(status=0)的任务
				var taskCount int64
				if err := tx.Model(dproductionproctask.Model()).Where(
					"node_id = ? and proc_inst_id = ? and ((status = 1 and flag = true) or status = 0)",
					parallelNode.NodeID, procInst.ID).Count(&taskCount).Error; err != nil {
					return err
				}

				// 只有当没有已完成且有效的任务，且没有待处理的任务时，才创建新任务
				if taskCount == 0 {
					procTaskObjects = append(procTaskObjects, map[string]interface{}{
						"CreatedAt":  time.Now(),
						"NodeName":   parallelNode.Name,
						"NodeID":     parallelNode.NodeID,
						"PrevNodeID": parallelNode.PrevNodeID,
						"ProcInstID": procInst.ID,
						"Assignee":   parallelNode.Assignee,
						"Status":     0,
						"Flag":       true,
					})
				}
			}

			// 如果是并行节点回退，且发起回退的节点不是目标回退节点，创建发起回退的节点记录
			fmt.Println(isParallelRollback, rollbackSourceNodeID, prevTask.NodeID)
			if isParallelRollback && rollbackSourceNodeID != prevTask.NodeID {
				// 因为当前回退节点状态会被设置为回退状态status=2，所以需要创建新任务
				if rollbackSourceNode != nil {
					procTaskObjects = append(procTaskObjects, map[string]interface{}{
						"CreatedAt":  time.Now(),
						"NodeName":   rollbackSourceNode.Name,
						"NodeID":     rollbackSourceNode.NodeID,
						"PrevNodeID": rollbackSourceNode.PrevNodeID,
						"ProcInstID": procInst.ID,
						"Assignee":   rollbackSourceNode.Assignee,
						"Status":     0,
						"Flag":       true,
					})
				}
			}

		case 3:
			var users []*duser.ApprovalResponse
			if err := tx.Model(duser.Model()).Where("id in ?", []uint{userID, taskObject["UserID"].(uint)}).Find(&users).Error; err != nil {
				return err
			}
			var userMap = make(map[uint]*duser.ApprovalResponse)
			for _, user := range users {
				userMap[user.Id] = user
			}
			procTaskObjects = append(procTaskObjects, map[string]interface{}{
				"CreatedAt":  time.Now(),
				"NodeName":   task.NodeName,
				"NodeID":     task.NodeID,
				"PrevNodeID": task.PrevNodeID,
				"ProcInstID": procInst.ID,
				"Assignee":   taskObject["UserID"].(uint),
				"Status":     0,
				"Flag":       true,
			})
			if len(taskObject["Comment"].(string)) > 0 {
				taskObject["Comment"] = fmt.Sprintf("%s \n %s -> %s", taskObject["Comment"], userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			} else {
				taskObject["Comment"] = fmt.Sprintf("%s -> %s", userMap[userID].Name, userMap[taskObject["UserID"].(uint)].Name)
			}
			//更新节点信息
			dproductionprocdef.UpdateAssignee(nodes, task.NodeID, taskObject["UserID"].(uint))
			nodeResource, err := dproductionprocdef.NodesToJSON(nodes)
			if err != nil {
				return err
			}
			// 更新流程实例的resource值
			if err := tx.Model(dproductionprocinst.Model()).Where("id = ?", procInst.ID).Update("resource", nodeResource).Error; err != nil {
				return err
			}

		case 4:
			featureObject["Status"] = 4
			// featureObject["version"] = production.MainProgramFileMd5 + "_deleted_at_" + time.Now().Format("20160102150405")
		}

		delete(taskObject, "NextNodeID")
		delete(taskObject, "UserID")

		if len(procTaskObjects) > 0 {
			//判断新建节点类型
			// fmt.Print(procTaskObjects)
			todoTask := procTaskObjects
			switch taskObject["Status"].(uint) {
			case 1:
				//提取回滚节点
				rollBackIDs := []string{}
				for _, task := range procTaskObjects {
					rollBackIDs = append(rollBackIDs, task["NodeID"].(string))
				}
				//查找是否存在回滚节点
				rollBackTasks := []*dproductionproctask.Response{}
				if err := tx.Model(dproductionproctask.Model()).Where("node_id in ?  and proc_inst_id = ? and status = 0 and flag = 0 ", rollBackIDs, procInst.ID).Find(&rollBackTasks).Error; err != nil {
					return err
				}
				//存在的情况下,重置相关任务信息，flag置为true
				if len(rollBackTasks) > 0 {
					if err := tx.Model(dproductionproctask.Model()).Where("node_id in ? and proc_inst_id = ? and status = 0 and flag = 0", rollBackIDs, procInst.ID).
						UpdateColumns(map[string]interface{}{"status": 0, "flag": true}).Error; err != nil {
						return err
					}
					// 移除回滚节点
					for _, task := range rollBackTasks {
						procTaskObjects = RemoveNodeID(procTaskObjects, task.NodeID)
					}
				} else {
					//不存在的情况下,判断是否为迭代发布，如果是，则需要获取最新上次版本的数据，获取相同节点的任务数据，补充填充相关内容platform_mg，chief_tester,cpld_check
					if production.ProductionType != "1" {
						productionCheck := dproduction.Response{}
						if err := tx.Model(dproduction.Model()).Where("id = ?", production.ProductionBaseVersion).Find(&productionCheck).Error; err != nil {
							return err
						}
						//需要获取最新上次版本的数据实例
						procinstCheck := dproductionprocinst.Response{}
						if err := tx.Model(dproductionprocinst.Model()).Where("production_id = ?", productionCheck.ID).Find(&procinstCheck).Error; err != nil {
							return err
						}
						//查找相同节点的任务数据 platform_mg，chief_tester,cpld_check
						for _, rollBackID := range rollBackIDs {
							// 对于 pm_submit 节点，我们不需要从上一个版本复制数据，因为它是新添加的节点
							if rollBackID == "pm_submit" {
								continue
							}

							if rollBackID == "platform_mg" {
								lastPlatformTask := dproductionproctask.Response{}
								if err := tx.Model(dproductionproctask.Model()).Last(&lastPlatformTask, "node_id ='platform_mg' and proc_inst_id =? and status = 1 and flag = true", procinstCheck.ID).Error; err != nil {
									return err
								}
								tempObject := FindByNodeID(procTaskObjects, rollBackID)
								if tempObject == nil {
									continue
								}
								// 移除节点信息
								procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
								//构造节点数据
								taskObject := map[string]interface{}{
									"CreatedAt":     time.Now(),
									"NodeName":      tempObject["NodeName"],
									"NodeID":        tempObject["NodeID"],
									"PrevNodeID":    tempObject["PrevNodeID"],
									"ProcInstID":    tempObject["ProcInstID"],
									"Assignee":      tempObject["Assignee"],
									"Status":        0,
									"Flag":          true,
									"Uboot":         lastPlatformTask.Uboot,
									"UbootMd5":      lastPlatformTask.UbootMd5,
									"UbootVersion":  lastPlatformTask.UbootVersion,
									"UbootDoc":      lastPlatformTask.UbootDoc,
									"SetmacTool":    lastPlatformTask.SetmacTool,
									"SetmacToolMd5": lastPlatformTask.SetmacToolMd5,
									"SetmacDoc":     lastPlatformTask.SetmacDoc,
									"SetmacIni":     lastPlatformTask.SetmacIni,
									"SetmacIniMd5":  lastPlatformTask.SetmacIniMd5,
									"RomProgram":    lastPlatformTask.RomProgram,
									"RomProgramMd5": lastPlatformTask.RomProgramMd5,
									"Done":          true,
								}
								if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
									return err
								}
								//查看是否存在SetmacHosts主机
								lastPlatformHosts := []*dproductionproctaskhost.Response{}
								if err := tx.Model(dproductionproctaskhost.Model()).Where("proc_inst_id =? ", procinstCheck.ID).Find(&lastPlatformHosts).Error; err != nil {
									return err
								}
								if len(lastPlatformHosts) > 0 {
									curPlatformTask := dproductionproctask.Response{}
									if err := tx.Model(dproductionproctask.Model()).Where("node_id =? and proc_inst_id =? and status = 0 and flag = 1", tempObject["NodeID"], procInst.ID).
										Order(fmt.Sprintf("%s %s", "created_at", "desc")).Find(&curPlatformTask).Error; err != nil {
										return err
									}
									if curPlatformTask.ID == 0 {
										return errors.New("获取节点数据失败")
									}
									//存在主机，则需要更新
									setmacHostObjects := []map[string]interface{}{}
									for _, host := range lastPlatformHosts {
										setmacHostObjects = append(setmacHostObjects, map[string]interface{}{
											"CreatedAt":     time.Now(),
											"TaskID":        curPlatformTask.ID,
											"ExtensionName": host.ExtensionName,
											"ExtensionTool": host.ExtensionTool,
											"MD5":           host.MD5,
											"ProcInstID":    procInst.ID,
										})
									}
									if err := tx.Model(dproductionproctaskhost.Model()).Create(setmacHostObjects).Error; err != nil {
										return err
									}
								}
								//查看是否存在Setmac扩展项
								lastPlatformExtensions := []*dproductionproctaskextension.Response{}
								if err := tx.Model(dproductionproctaskextension.Model()).Where("proc_inst_id =? ", procinstCheck.ID).Find(&lastPlatformExtensions).Error; err != nil {
									return err
								}
								if len(lastPlatformExtensions) > 0 {
									curPlatformTask := dproductionproctask.Response{}
									if err := tx.Model(dproductionproctask.Model()).Where("node_id =? and proc_inst_id =? and status = 0 and flag = 1", tempObject["NodeID"], procInst.ID).
										Order(fmt.Sprintf("%s %s", "created_at", "desc")).Find(&curPlatformTask).Error; err != nil {
										return err
									}
									if curPlatformTask.ID == 0 {
										return errors.New("获取节点数据失败")
									}
									//存在扩展项，则需要更新
									setmacObjects := []map[string]interface{}{}
									for _, extension := range lastPlatformExtensions {
										setmacObjects = append(setmacObjects, map[string]interface{}{
											"CreatedAt":     time.Now(),
											"TaskID":        curPlatformTask.ID,
											"ExtensionName": extension.ExtensionName,
											"ExtensionTool": extension.ExtensionTool,
											"MD5":           extension.MD5,
											"ExtensionDoc":  extension.ExtensionDoc,
											"ProcInstID":    procInst.ID,
										})
									}
									if err := tx.Model(dproductionproctaskextension.Model()).Create(setmacObjects).Error; err != nil {
										return err
									}
								}
							}
							if rollBackID == "chief_tester" {
								lastChiefTask := dproductionproctask.Response{}
								if err := tx.Model(dproductionproctask.Model()).Last(&lastChiefTask, "node_id ='chief_tester' and proc_inst_id =? and status = 1 and flag = true", procinstCheck.ID).Error; err != nil {
									return err
								}
								tempObject := FindByNodeID(procTaskObjects, rollBackID)
								if tempObject == nil {
									continue
								}
								// 移除节点信息
								procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
								//构造节点数据
								taskObject := map[string]interface{}{
									"CreatedAt":           time.Now(),
									"NodeName":            tempObject["NodeName"],
									"NodeID":              tempObject["NodeID"],
									"PrevNodeID":          tempObject["PrevNodeID"],
									"ProcInstID":          tempObject["ProcInstID"],
									"Assignee":            tempObject["Assignee"],
									"Status":              0,
									"Flag":                true,
									"ManufTestProgram":    lastChiefTask.ManufTestProgram,
									"ManufTestProgramDoc": lastChiefTask.ManufTestProgramDoc,
									"ManufTestReport":     lastChiefTask.ManufTestReport,
									"ManufTestProgramMD5": lastChiefTask.ManufTestProgramMD5,
									"Done":                true,
								}
								if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
									return err
								}
							}

							if rollBackID == "cpld_check" {
								lastCpldTask := dproductionproctask.Response{}
								if err := tx.Model(dproductionproctask.Model()).Last(&lastCpldTask, "node_id ='cpld_check' and proc_inst_id =? and status = 1 and flag = true", procinstCheck.ID).Error; err != nil {
									return err
								}
								tempObject := FindByNodeID(procTaskObjects, rollBackID)
								if tempObject == nil {
									continue
								}
								// 移除节点信息
								procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
								//构造节点数据
								taskObject := map[string]interface{}{
									"CreatedAt":     time.Now(),
									"NodeName":      tempObject["NodeName"],
									"NodeID":        tempObject["NodeID"],
									"PrevNodeID":    tempObject["PrevNodeID"],
									"ProcInstID":    tempObject["ProcInstID"],
									"Assignee":      tempObject["Assignee"],
									"Status":        0,
									"Flag":          true,
									"CpldUpdate":    lastCpldTask.CpldUpdate,
									"CpldUpdateUrl": lastCpldTask.CpldUpdateUrl,
									"Done":          true,
								}
								if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
									return err
								}
							}
						}
					}

				}
			case 3:
				//提取节点数据
				rollBackIDs := []string{}
				for _, task := range procTaskObjects {
					rollBackIDs = append(rollBackIDs, task["NodeID"].(string))
				}
				if production.ProductionType != "1" {
					productionCheck := dproduction.Response{}
					if err := tx.Model(dproduction.Model()).Where("id = ?", production.ProductionBaseVersion).Find(&productionCheck).Error; err != nil {
						return err
					}
					//需要获取最新上次版本的数据实例
					procinstCheck := dproductionprocinst.Response{}
					if err := tx.Model(dproductionprocinst.Model()).Where("production_id = ?", productionCheck.ID).Find(&procinstCheck).Error; err != nil {
						return err
					}
					//查找相同节点的任务数据 platform_mg，chief_tester,cpld_check
					for _, rollBackID := range rollBackIDs {
						// 对于 pm_submit 节点，我们不需要从上一个版本复制数据，因为它是新添加的节点
						if rollBackID == "pm_submit" {
							continue
						}

						if rollBackID == "platform_mg" {
							lastPlatformTask := dproductionproctask.Response{}
							if err := tx.Model(dproductionproctask.Model()).Last(&lastPlatformTask, "node_id ='platform_mg' and proc_inst_id =? and status = 1 and flag = true", procinstCheck.ID).Error; err != nil {
								return err
							}
							tempObject := FindByNodeID(procTaskObjects, rollBackID)
							if tempObject == nil {
								continue
							}
							// 移除节点信息
							procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
							//构造节点数据
							taskObject := map[string]interface{}{
								"CreatedAt":     time.Now(),
								"NodeName":      tempObject["NodeName"],
								"NodeID":        tempObject["NodeID"],
								"PrevNodeID":    tempObject["PrevNodeID"],
								"ProcInstID":    tempObject["ProcInstID"],
								"Assignee":      tempObject["Assignee"],
								"Status":        0,
								"Flag":          true,
								"Uboot":         lastPlatformTask.Uboot,
								"UbootMd5":      lastPlatformTask.UbootMd5,
								"UbootVersion":  lastPlatformTask.UbootVersion,
								"UbootDoc":      lastPlatformTask.UbootDoc,
								"SetmacTool":    lastPlatformTask.SetmacTool,
								"SetmacToolMd5": lastPlatformTask.SetmacToolMd5,
								"SetmacDoc":     lastPlatformTask.SetmacDoc,
								"SetmacIni":     lastPlatformTask.SetmacIni,
								"SetmacIniMd5":  lastPlatformTask.SetmacIniMd5,
								"RomProgram":    lastPlatformTask.RomProgram,
								"RomProgramMd5": lastPlatformTask.RomProgramMd5,
								"Done":          true,
							}
							if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
								return err
							}
							//查看是否存在SetmacHosts主机
							lastPlatformHosts := []*dproductionproctaskhost.Response{}
							if err := tx.Model(dproductionproctaskhost.Model()).Where("proc_inst_id =? ", procinstCheck.ID).Find(&lastPlatformHosts).Error; err != nil {
								return err
							}
							if len(lastPlatformHosts) > 0 {
								curPlatformTask := dproductionproctask.Response{}
								if err := tx.Model(dproductionproctask.Model()).Where("node_id =? and proc_inst_id =? and status = 0 and flag = 1", tempObject["NodeID"], procInst.ID).
									Order(fmt.Sprintf("%s %s", "created_at", "desc")).Find(&curPlatformTask).Error; err != nil {
									return err
								}
								if curPlatformTask.ID == 0 {
									return errors.New("获取节点数据失败")
								}
								//存在主机，则需要更新
								setmacHostObjects := []map[string]interface{}{}
								for _, host := range lastPlatformHosts {
									setmacHostObjects = append(setmacHostObjects, map[string]interface{}{
										"CreatedAt":     time.Now(),
										"TaskID":        curPlatformTask.ID,
										"ExtensionName": host.ExtensionName,
										"ExtensionTool": host.ExtensionTool,
										"MD5":           host.MD5,
										"ProcInstID":    procInst.ID,
									})
								}
								if err := tx.Model(dproductionproctaskhost.Model()).Create(setmacHostObjects).Error; err != nil {
									return err
								}
							}
							//查看是否存在Setmac扩展项
							lastPlatformExtensions := []*dproductionproctaskextension.Response{}
							if err := tx.Model(dproductionproctaskextension.Model()).Where("proc_inst_id =? ", procinstCheck.ID).Find(&lastPlatformExtensions).Error; err != nil {
								return err
							}
							if len(lastPlatformExtensions) > 0 {
								curPlatformTask := dproductionproctask.Response{}
								if err := tx.Model(dproductionproctask.Model()).Where("node_id =? and proc_inst_id =? and status = 0 and flag = 1", tempObject["NodeID"], procInst.ID).
									Order(fmt.Sprintf("%s %s", "created_at", "desc")).Find(&curPlatformTask).Error; err != nil {
									return err
								}
								if curPlatformTask.ID == 0 {
									return errors.New("获取节点数据失败")
								}
								//存在扩展项，则需要更新
								setmacObjects := []map[string]interface{}{}
								for _, extension := range lastPlatformExtensions {
									setmacObjects = append(setmacObjects, map[string]interface{}{
										"CreatedAt":     time.Now(),
										"TaskID":        curPlatformTask.ID,
										"ExtensionName": extension.ExtensionName,
										"ExtensionTool": extension.ExtensionTool,
										"MD5":           extension.MD5,
										"ExtensionDoc":  extension.ExtensionDoc,
										"ProcInstID":    procInst.ID,
									})
								}
								if err := tx.Model(dproductionproctaskextension.Model()).Create(setmacObjects).Error; err != nil {
									return err
								}
							}
						}
						if rollBackID == "chief_tester" {
							lastChiefTask := dproductionproctask.Response{}
							if err := tx.Model(dproductionproctask.Model()).Last(&lastChiefTask, "node_id ='chief_tester' and proc_inst_id =? and status = 1 and flag = 1", procinstCheck.ID).Error; err != nil {
								return err
							}
							tempObject := FindByNodeID(procTaskObjects, rollBackID)
							if tempObject == nil {
								continue
							}
							// 移除节点信息
							procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
							//构造节点数据
							taskObject := map[string]interface{}{
								"CreatedAt":           time.Now(),
								"NodeName":            tempObject["NodeName"],
								"NodeID":              tempObject["NodeID"],
								"PrevNodeID":          tempObject["PrevNodeID"],
								"ProcInstID":          tempObject["ProcInstID"],
								"Assignee":            tempObject["Assignee"],
								"Status":              0,
								"Flag":                true,
								"ManufTestProgram":    lastChiefTask.ManufTestProgram,
								"ManufTestProgramDoc": lastChiefTask.ManufTestProgramDoc,
								"ManufTestReport":     lastChiefTask.ManufTestReport,
								"ManufTestProgramMD5": lastChiefTask.ManufTestProgramMD5,
								"Done":                true,
							}
							if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
								return err
							}
						}

						if rollBackID == "cpld_check" {
							lastCpldTask := dproductionproctask.Response{}
							if err := tx.Model(dproductionproctask.Model()).Last(&lastCpldTask, "node_id ='cpld_check' and proc_inst_id =? and status = 1 and flag = 1", procinstCheck.ID).Error; err != nil {
								return err
							}
							tempObject := FindByNodeID(procTaskObjects, rollBackID)
							if tempObject == nil {
								continue
							}
							// 移除节点信息
							procTaskObjects = RemoveNodeID(procTaskObjects, rollBackID)
							//构造节点数据
							taskObject := map[string]interface{}{
								"CreatedAt":     time.Now(),
								"NodeName":      tempObject["NodeName"],
								"NodeID":        tempObject["NodeID"],
								"PrevNodeID":    tempObject["PrevNodeID"],
								"ProcInstID":    tempObject["ProcInstID"],
								"Assignee":      tempObject["Assignee"],
								"Status":        0,
								"Flag":          true,
								"CpldUpdate":    lastCpldTask.CpldUpdate,
								"CpldUpdateUrl": lastCpldTask.CpldUpdateUrl,
								"Done":          true,
							}
							if err := tx.Model(dproductionproctask.Model()).Create(taskObject).Error; err != nil {
								return err
							}
						}
					}
				}
			}
			if len(procTaskObjects) > 0 {
				if err := tx.Model(dproductionproctask.Model()).Create(procTaskObjects).Error; err != nil {
					return err
				}
			}

			go SendMail(todoTask, production.ID, production.Urgency)
		}
		if err := tx.Model(dproductionproctask.Model()).Where("id = ?", taskID).Updates(taskObject).Error; err != nil {
			return err
		}
		// 将相同nodeid的任务置为完成

		/*
			taskObject := map[string]interface{}{
				"NodeID":     request.NodeID,
				"UpdatedAt":  time.Now(),
				"Status":     request.Status,
				"NextNodeID": request.NextNodeID,
				"Comment":    request.Comment,
				"Attachment": fileName,
				"UserID":     request.UserID,
			}
		*/
		_taskObject := map[string]interface{}{
			"NodeID":    taskObject["NodeID"],
			"UpdatedAt": time.Now(),
			"Status":    taskObject["Status"],
			"Comment":   fmt.Sprintf("无需处理"),
		}
		if err := tx.Model(dproductionproctask.Model()).Where(" proc_inst_id = ? and id != ? and node_id = ? and node_name != ?", procInstID, taskID, taskObject["NodeID"].(string), task.NodeName).Updates(_taskObject).Error; err != nil {
			return err
		}

		if err := tx.Model(dproduction.Model()).Where("id = ?", procInst.ProductionID).UpdateColumns(featureObject).Error; err != nil {
			return err
		}
		// pm_submit 环节通过  taskObject["Status"].(uint) 去除多余基础数据
		if task.NodeID == "pm_submit" && taskObject["Status"].(uint) == 1 {
			featureObject["Title"] = featureObject["MainProgramFileName"]
			delete(featureObject, "MainProgramUrl")
			delete(featureObject, "MainProgramFileName")
			delete(featureObject, "MainProgramFileSize")
			delete(featureObject, "MainProgramFileMd5")
			delete(featureObject, "OspkgInstallUrl")
			delete(featureObject, "OspkgInstallName")
			delete(featureObject, "OspkgInstallSize")
			delete(featureObject, "OspkgInstallMd5")
			delete(featureObject, "SoftwareNumber")
			delete(featureObject, "SoftwareVersion")
		}

		// start 节点通过时，删除productionprocinst表中不存在的字段，防止更新时出错
		if task.NodeID == "start" && taskObject["Status"].(uint) == 1 {
			// ProductionProcInst表只有这些字段: ID, CreatedAt, UpdatedAt, Title, NodeID, TaskID, StartUserID, ProductionID, Status, Resource
			// 删除不存在于ProductionProcInst表中的字段
			delete(featureObject, "ProductionType")
			delete(featureObject, "VersionType")
			delete(featureObject, "RegionType")
			delete(featureObject, "ProductModel")
			delete(featureObject, "ReleaseDate")
			delete(featureObject, "VersionDesc")
			delete(featureObject, "Urgency")
			delete(featureObject, "MainProgramUrl")
			delete(featureObject, "MainProgramFileName")
			delete(featureObject, "MainProgramFileSize")
			delete(featureObject, "MainProgramFileMd5")
			delete(featureObject, "OspkgInstallUrl")
			delete(featureObject, "OspkgInstallName")
			delete(featureObject, "OspkgInstallSize")
			delete(featureObject, "OspkgInstallMd5")
			delete(featureObject, "SoftwareNumber")
			delete(featureObject, "SoftwareVersion")
			delete(featureObject, "ProductSeries")
			delete(featureObject, "ProductionVersions")
			delete(featureObject, "Version")
			delete(featureObject, "OverTimeNotice")
			delete(featureObject, "Uuid")
			// 只保留: Status, NodeID, TaskID, Title
		}

		// 在更新productionprocinst表之前，确保删除表中不存在的字段，防止更新时出错
		// 创建一个仅包含productionprocinst表中存在的字段的新对象
		procInstUpdateObject := map[string]interface{}{
			"UpdatedAt": time.Now(),
		}

		// 只复制productionprocinst表中存在的字段
		if nodeID, ok := featureObject["NodeID"]; ok {
			procInstUpdateObject["NodeID"] = nodeID
		}
		if taskID, ok := featureObject["TaskID"]; ok {
			procInstUpdateObject["TaskID"] = taskID
		}
		if title, ok := featureObject["Title"]; ok {
			procInstUpdateObject["Title"] = title
		}

		// 更新productionprocinst表，使用过滤后的对象
		if err := tx.Model(dproductionprocinst.Model()).Where("id = ?", procInst.ID).UpdateColumns(procInstUpdateObject).Error; err != nil {
			return err
		}

		if featureObject["Status"] == 1 {
			allTasks := []*dproductionproctask.Response{}
			if err := tx.Model(dproductionproctask.Model()).Where("proc_inst_id = ? and status = 1 and flag = true", procInst.ID).Find(&allTasks).Error; err != nil {
				return err
			}
			go SendMailSuccess(allTasks, production.ID, production.Urgency)
		}
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateProcInstProcessTransaction(procInstID uint, resource string, nodes []*dproductionprocdef.Node, nodeMap map[string]uint) error {
	db := easygorm.GetEasyGormDb()
	err := db.Transaction(func(tx *gorm.DB) error {
		// 更新流程实例的resource值
		if err := tx.Model(dproductionprocinst.Model()).Where("id = ?", procInstID).Update("resource", resource).Error; err != nil {
			return err
		}
		//循环遍历nodemap 更新任务节点数据 提取任务节点 done =0 AND statu=0 AND node_id = 'node_id'
		for _, node := range nodes {
			if err := tx.Model(dproductionproctask.Model()).
				Where("proc_inst_id = ? and node_id = ? and status = 0 and done = 0", procInstID, node.NodeID).
				Update("assignee", nodeMap[node.NodeID]).Error; err != nil {
				return err
			}
		}
		// 返回 nil 提交事务
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func SendMail(procTaskObjects []map[string]interface{}, featureID uint, urgency bool) {
	for _, procTaskObject := range procTaskObjects {
		if procTaskObject["Status"] == 0 {
			subject := fmt.Sprintf("[下生产管理系统][下生产ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))

			if urgency {
				subject = fmt.Sprintf("[紧急][下生产管理系统][下生产ID:%d][%s][待处理]", featureID, procTaskObject["NodeName"].(string))
			}

			body := fmt.Sprintf(`%s<br><p>下生产链接: <a href="http://10.51.134.126/productionSYS/#/prodSys/todo">http://10.51.134.126/productionSYS/#/prodSys/todo</a><p>`, subject)
			if !libs.Config.Debug {
				err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body, []string{})
				// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
				if err != nil {
					logging.ErrorLogger.Error(err)
				}
			}
			logging.DebugLogger.Debugf("send mail", []string{fmt.Sprintf("%<EMAIL>", duser.UserMap[procTaskObject["Assignee"].(uint)].Username)}, subject, body)
		}
	}
}

func SendMailSuccess(taskObjects []*dproductionproctask.Response, featureID uint, urgency bool) {
	mailTo := []string{"<EMAIL>", "<EMAIL>"}
	for _, taskObject := range taskObjects {
		if !libs.InArrayS(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[taskObject.Assignee].Username)) {
			mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[taskObject.Assignee].Username))
		}
	}
	subject := fmt.Sprintf("[下生产管理系统][下生产库ID:%d][已发布成功]", featureID)
	if urgency {
		subject = fmt.Sprintf("[紧急][下生产管理系统][下生产库ID:%d][已发布成功]", featureID)
	}
	body := fmt.Sprintf(`%s<br><p>下生产库链接: <a href="http://10.51.134.126/productionSYS/#/prodSys/success">http://10.51.134.126/productionSYS/#/prodSys/success</a><p>`, subject)
	if !libs.Config.Debug {
		err := libs.SendMail(mailTo, subject, body, []string{})
		// err := libs.SendMail([]string{fmt.Sprintf("%<EMAIL>", "linjiakai")}, subject, body)
		if err != nil {
			logging.ErrorLogger.Error(err)
		}
	}
	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)
}

func UniqueStrings(input []string) []string {
	uniqueMap := make(map[string]bool)
	var uniqueSlice []string

	for _, value := range input {
		if _, exists := uniqueMap[value]; !exists {
			uniqueMap[value] = true
			uniqueSlice = append(uniqueSlice, value)
		}
	}

	return uniqueSlice
}

func RemoveNodeID(procTaskObjects []map[string]interface{}, nodeIDToRemove string) []map[string]interface{} {
	filteredProcTaskObjects := make([]map[string]interface{}, 0)
	for _, taskObject := range procTaskObjects {
		if nodeID, ok := taskObject["NodeID"].(string); !ok || nodeID != nodeIDToRemove {
			filteredProcTaskObjects = append(filteredProcTaskObjects, taskObject)
		}
	}
	return filteredProcTaskObjects
}

func FindByNodeID(procTaskObjects []map[string]interface{}, nodeID string) map[string]interface{} {
	for _, taskObject := range procTaskObjects {
		if taskObject["NodeID"] == nodeID {
			return taskObject
		}
	}

	return nil
}
