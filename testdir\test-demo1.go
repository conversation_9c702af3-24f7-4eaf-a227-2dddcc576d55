package main

import (
	"bufio"
	"encoding/xml"
	"fmt"
	"os"
	"strings"

	"github.com/beevik/etree"
)

// FirewallAddress 定义防火墙地址结构
type FirewallAddress struct {
	Name     string
	Type     string
	StartIP  string
	EndIP    string
	Subnet   string
	Mask     string
}

// FirewallAddrGroup 定义地址组结构
type FirewallAddrGroup struct {
	Name    string
	Members []string
}

// ServiceGroup 定义服务组结构
type ServiceGroup struct {
	Name    string
	Members []string
}

// Policy 定义防火墙策略结构
type Policy struct {
	Name       string
	SrcAddr    string
	DstAddr    string
	Service    string
	Status     string
	FixedPort  string
	Comments   string
}

// AVProfile 定义防病毒配置结构
type AVProfile struct {
	Name       string
	Description string
	ScanMode   string
	Protocols  []Protocol
	FileTypes  []string
}

// Protocol 定义协议结构
type Protocol struct {
	Name      string
	Direction string
}

// Service 定义服务结构
type Service struct {
	Name      string
	TCPPorts  []string
	UDPPorts  []string
}

// IPPool 定义IP池结构
type IPPool struct {
	Name    string
	StartIP string
	EndIP   string
}

// IPSensor 定义IPS传感器结构
type IPSensor struct {
	Name     string
	Comment  string
	Severity []string
}

func parseFirewallAddress(lines []string) []FirewallAddress {
	var addresses []FirewallAddress
	var currentAddress *FirewallAddress
	insideAddressBlock := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall address") {
			insideAddressBlock = true
			continue
		}

		if insideAddressBlock {
			if strings.HasPrefix(line, "end") {
				if currentAddress != nil {
					addresses = append(addresses, *currentAddress)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentAddress != nil {
					addresses = append(addresses, *currentAddress)
				}
				name := extractQuotedString(line)
				currentAddress = &FirewallAddress{
					Name:     name,
					Type:     "",
					StartIP:  "",
					EndIP:    "",
					Subnet:   "",
					Mask:     "",
				}
			} else if strings.HasPrefix(line, "set type") {
				if currentAddress != nil {
					currentAddress.Type = strings.Fields(line)[-1]
				}
			} else if strings.HasPrefix(line, "set start-ip") {
				if currentAddress != nil {
					currentAddress.StartIP = strings.Fields(line)[-1]
				}
			} else if strings.HasPrefix(line, "set end-ip") {
				if currentAddress != nil {
					currentAddress.EndIP = strings.Fields(line)[-1]
				}
			} else if strings.HasPrefix(line, "set subnet") {
				if currentAddress != nil {
					parts := strings.Fields(line)
					currentAddress.Subnet = parts[len(parts)-2]
					currentAddress.Mask = parts[len(parts)-1]
				}
			}
		}
	}

	var validAddresses []FirewallAddress
	for _, addr := range addresses {
		if (addr.Type == "iprange" && addr.StartIP != "" && addr.EndIP != "") ||
			(addr.Subnet != "" && addr.Mask != "") {
			validAddresses = append(validAddresses, addr)
		}
	}

	return validAddresses
}

func parseFirewallAddrGrp(lines []string) []FirewallAddrGroup {
	var groups []FirewallAddrGroup
	var currentGroup *FirewallAddrGroup
	insideGroup := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall addrgrp") {
			insideGroup = true
			continue
		}

		if insideGroup {
			if strings.HasPrefix(line, "end") {
				if currentGroup != nil {
					groups = append(groups, *currentGroup)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentGroup != nil {
					groups = append(groups, *currentGroup)
				}
				name := extractQuotedString(line)
				currentGroup = &FirewallAddrGroup{
					Name:    name,
					Members: []string{},
				}
			} else if strings.HasPrefix(line, "set member") {
				members := extractMultipleQuotedStrings(line)
				if currentGroup != nil {
					currentGroup.Members = append(currentGroup.Members, members...)
				}
			}
		}
	}

	return groups
}

func parseServiceGroup(lines []string) []ServiceGroup {
	var serviceGroups []ServiceGroup
	var currentGroup *ServiceGroup
	insideGroup := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall service group") {
			insideGroup = true
			continue
		}

		if insideGroup {
			if strings.HasPrefix(line, "end") {
				if currentGroup != nil {
					serviceGroups = append(serviceGroups, *currentGroup)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentGroup != nil {
					serviceGroups = append(serviceGroups, *currentGroup)
				}
				name := extractQuotedString(line)
				currentGroup = &ServiceGroup{
					Name:    name,
					Members: []string{},
				}
			} else if strings.HasPrefix(line, "set member") {
				members := extractMultipleQuotedStrings(line)
				if currentGroup != nil {
					currentGroup.Members = append(currentGroup.Members, members...)
				}
			}
		}
	}

	return serviceGroups
}

func parseFortinetPolicy(lines []string) []Policy {
	var policies []Policy
	var currentPolicy *Policy
	insidePolicyBlock := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall policy") {
			insidePolicyBlock = true
			continue
		}

		if insidePolicyBlock {
			if strings.HasPrefix(line, "end") {
				if currentPolicy != nil {
					policies = append(policies, *currentPolicy)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentPolicy != nil {
					policies = append(policies, *currentPolicy)
				}
				currentPolicy = &Policy{
					Name:      "",
					SrcAddr:   "",
					DstAddr:   "",
					Service:   "",
					Status:    "",
					FixedPort: "",
					Comments:  "",
				}
			} else if strings.HasPrefix(line, "set name") {
				if currentPolicy != nil {
					currentPolicy.Name = extractQuotedString(line)
				}
			} else if strings.HasPrefix(line, "set srcaddr") {
				if currentPolicy != nil {
					currentPolicy.SrcAddr = extractQuotedString(line)
				}
			} else if strings.HasPrefix(line, "set dstaddr") {
				if currentPolicy != nil {
					currentPolicy.DstAddr = extractQuotedString(line)
				}
			} else if strings.HasPrefix(line, "set service") {
				if currentPolicy != nil {
					currentPolicy.Service = extractQuotedString(line)
				}
			} else if strings.HasPrefix(line, "set status") {
				if currentPolicy != nil {
					currentPolicy.Status = strings.Fields(line)[-1]
				}
			} else if strings.HasPrefix(line, "set fixedport") {
				if currentPolicy != nil {
					currentPolicy.FixedPort = strings.Fields(line)[-1]
				}
			} else if strings.HasPrefix(line, "set comments") {
				if currentPolicy != nil {
					currentPolicy.Comments = extractQuotedString(line)
				}
			}
		}
	}

	return policies
}

func extractQuotedString(s string) string {
	parts := strings.Split(s, "\"")
	if len(parts) >= 2 {
		return parts[1]
	}
	return ""
}

func extractMultipleQuotedStrings(s string) []string {
	var results []string
	parts := strings.Split(s, "\"")
	for i := 1; i < len(parts); i += 2 {
		results = append(results, parts[i])
	}
	return results
}

func parseAntiVirus(lines []string) []AVProfile {
	var profiles []AVProfile
	var currentProfile *AVProfile
	insideAVBlock := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config antivirus profile") {
			insideAVBlock = true
			continue
		}

		if insideAVBlock {
			if strings.HasPrefix(line, "end") {
				if currentProfile != nil {
					profiles = append(profiles, *currentProfile)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentProfile != nil {
					profiles = append(profiles, *currentProfile)
				}
				name := extractQuotedString(line)
				currentProfile = &AVProfile{
					Name:       name,
					Description: fmt.Sprintf("Profile for %s", name),
					ScanMode:   "",
					Protocols:  []Protocol{},
					FileTypes:  []string{},
				}
			} else if strings.HasPrefix(line, "set scan-mode") {
				if currentProfile != nil {
					currentProfile.ScanMode = strings.Fields(line)[-1]
				}
			} else if strings.HasPrefix(line, "set protocols") {
				if currentProfile != nil {
					protocols := extractMultipleQuotedStrings(line)
					for _, p := range protocols {
						currentProfile.Protocols = append(currentProfile.Protocols, Protocol{
							Name:      p,
							Direction: "both",
						})
					}
				}
			} else if strings.HasPrefix(line, "set file-types") {
				if currentProfile != nil {
					fileTypes := extractMultipleQuotedStrings(line)
					currentProfile.FileTypes = append(currentProfile.FileTypes, fileTypes...)
				}
			}
		}
	}

	return profiles
}

func parseServiceCustom(lines []string) []Service {
	var services []Service
	var currentService *Service
	insideServiceCustom := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall service custom") {
			insideServiceCustom = true
			continue
		}

		if insideServiceCustom {
			if strings.HasPrefix(line, "end") {
				if currentService != nil {
					services = append(services, *currentService)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentService != nil {
					services = append(services, *currentService)
				}
				name := extractQuotedString(line)
				currentService = &Service{
					Name:     name,
					TCPPorts: []string{},
					UDPPorts: []string{},
				}
			} else if strings.HasPrefix(line, "set tcp-portrange") {
				if currentService != nil {
					ports := strings.Fields(line)[-1]
					currentService.TCPPorts = append(currentService.TCPPorts, strings.Split(ports, ",")...)
				}
			} else if strings.HasPrefix(line, "set udp-portrange") {
				if currentService != nil {
					ports := strings.Fields(line)[-1]
					currentService.UDPPorts = append(currentService.UDPPorts, strings.Split(ports, ",")...)
				}
			}
		}
	}

	return services
}

func parseFirewallIPPool(lines []string) []IPPool {
	var ippools []IPPool
	var currentIPPool *IPPool
	insideIPPoolBlock := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "config firewall ippool") {
			insideIPPoolBlock = true
			continue
		}

		if insideIPPoolBlock {
			if strings.HasPrefix(line, "end") {
				if currentIPPool != nil {
					ippools = append(ippools, *currentIPPool)
				}
				break
			} else if strings.HasPrefix(line, "edit") {
				if currentIPPool != nil {
					ippools = append(ippools, *currentIPPool)
				}
				name := extractQuotedString(line)
				currentIPPool = &IPPool{
					Name:    name,
					StartIP: "",
					EndIP:   "",
				}
			} else if strings.HasPrefix(line, "set startip") {
				if currentIPPool != nil {
					currentIPPool.StartIP = strings.Fields(line)[-1]
				}
			} else if strings.HasPrefix(line, "set endip") {
				if currentIPPool != nil {
					currentIPPool.EndIP = strings.Fields(line)[-1]
				}
			}
		}
	}

	return ippools
}

func parseIPSSensor(lines []string) []IPSensor {
	var sensors []IPSensor
	var currentSensor *IPSensor
	insideIPSSensor := false
	nestedBlocks := []string{}

	scanner := bufio.NewScanner(strings.NewReader(strings.Join(lines, "\n")))
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())

		if strings.HasPrefix(line, "config ips sensor") {
			insideIPSSensor = true
			continue
		}

		if insideIPSSensor {
			if line == "end" && len(nestedBlocks) == 0 {
				if currentSensor != nil {
					sensors = append(sensors, *currentSensor)
				}
				break
			}

			if strings.HasPrefix(line, "config") || strings.HasPrefix(line, "edit") {
				nestedBlocks = append(nestedBlocks, line)
				if len(nestedBlocks) == 1 && strings.HasPrefix(line, "edit") {
					name := extractQuotedString(line)
					if currentSensor != nil {
						sensors = append(sensors, *currentSensor)
					}
					currentSensor = &IPSensor{
						Name:     name,
						Comment:  "",
						Severity: []string{},
					}
				}
				continue
			}

			if line == "end" || line == "next" {
				if len(nestedBlocks) > 0 {
					nestedBlocks = nestedBlocks[:len(nestedBlocks)-1]
					if len(nestedBlocks) == 0 && currentSensor != nil {
						sensors = append(sensors, *currentSensor)
						currentSensor = nil
					}
				}
				continue
			}

			if strings.HasPrefix(line, "set comment") && currentSensor != nil {
				comment := ""
				if strings.Contains(line, "\"") {
					comment = line[strings.Index(line, "\"")+1 : strings.LastIndex(line, "\"")]
				}
				currentSensor.Comment = comment
			}

			if strings.HasPrefix(line, "set severity") && currentSensor != nil {
				severities := strings.Fields(line)[2:]
				currentSensor.Severity = append(currentSensor.Severity, severities...)
			}

			if strings.HasPrefix(line, "config entries") && currentSensor != nil {
				for scanner.Scan() {
					entryLine := strings.TrimSpace(scanner.Text())
					if entryLine == "end" {
						break
					}
					if strings.HasPrefix(entryLine, "set severity") {
						severities := strings.Fields(entryLine)[2:]
						currentSensor.Severity = append(currentSensor.Severity, severities...)
					}
				}
			}
		}
	}

	return sensors
}

func generateRuijieNatConfig(root *etree.Element, policies []Policy, ippools []IPPool) error {
	natNode := root.FindElement(".//nat")
	if natNode == nil {
		return fmt.Errorf("无法找到 <nat> 节点，请检查模板文件")
	}

	for _, ippool := range ippools {
		enabledNode := natNode.CreateElement("enabled")
		enabledNode.SetText("true")

		poolNode := natNode.CreateElement("pool")
		etree.SubElement(poolNode, "name").SetText(ippool.Name)
		addressNode := etree.SubElement(poolNode, "address")
		addressNode.SetText(fmt.Sprintf("%s-%s", ippool.StartIP, ippool.EndIP))
	}

	for _, policy := range policies {
		rule := natNode.CreateElement("rule")
		etree.SubElement(rule, "name").SetText(policy.Name)
		etree.SubElement(rule, "desc").SetText(policy.Comments)

		ruleEn := etree.SubElement(rule, "rule_en")
		if policy.Status != "disable" {
			ruleEn.SetText("true")
		} else {
			ruleEn.SetText("false")
		}

		staticSNAT44 := etree.SubElement(rule, "static-snat44")
		match := etree.SubElement(staticSNAT44, "match")

		sourceNetwork := etree.SubElement(match, "source-network")
		etree.SubElement(sourceNetwork, "name").SetText(policy.SrcAddr)

		if policy.DstAddr != "" && strings.ToLower(policy.DstAddr) != "all" {
			destNetwork := etree.SubElement(match, "dest-network")
			etree.SubElement(destNetwork, "name").SetText(policy.DstAddr)
		}

		service := etree.SubElement(match, "service")
		etree.SubElement(service, "name").SetText(policy.Service)

		translateTo := etree.SubElement(staticSNAT44, "translate-to")
		etree.SubElement(translateTo, "output-address")
		noPAT := etree.SubElement(translateTo, "no-pat")
		if policy.FixedPort == "enable" {
			noPAT.SetText("true")
		} else {
			noPAT.SetText("false")
		}
	}

	return nil
}

func generateRuijieFirewallAddresses(root *etree.Element, firewallAddresses []FirewallAddress) error {
	networkObjNS := "urn:ruijie:ntos:params:xml:ns:yang:network-obj"
	networkObjNode := root.FindElement(".//network-obj")
	if networkObjNode == nil {
		return fmt.Errorf("无法找到 <network-obj> 节点，请检查模板文件")
	}

	for _, address := range firewallAddresses {
		if address.Name == "" || address.Type == "" {
			continue
		}
		if address.Type == "iprange" && (address.StartIP == "" || address.EndIP == "") {
			continue
		}
		if address.Type == "subnet" && (address.Subnet == "" || address.Mask == "") {
			continue
		}

		addressSetNode := etree.NewElement("address-set")
		addressSetNode.CreateElement("name").SetText(address.Name)

		ipSetNode := etree.SubElement(addressSetNode, "ip-set")
		ipAddressNode := etree.SubElement(ipSetNode, "ip-address")
		if address.Type == "iprange" {
			ipAddressNode.SetText(fmt.Sprintf("%s-%s", address.StartIP, address.EndIP))
		} else if address.Type == "subnet" {
			ipAddressNode.SetText(fmt.Sprintf("%s/%s", address.Subnet, address.Mask))
		}

		networkObjNode.AddChild(addressSetNode)
	}

	return nil
}

func generateRuijieServiceConfig(root *etree.Element, services []Service) error {
	serviceObjNode := root.FindElement(".//service-obj")
	if serviceObjNode == nil {
		return fmt.Errorf("无法找到 <service-obj> 节点，请检查模板文件")
	}

	for _, service := range services {
		serviceSet := etree.SubElement(serviceObjNode, "service-set")
		etree.SubElement(serviceSet, "name").SetText(service.Name)

		if len(service.TCPPorts) > 0 {
			tcpNode := etree.SubElement(serviceSet, "tcp")
			destPort := strings.Join(service.TCPPorts, ",")
			etree.SubElement(tcpNode, "dest-port").SetText(destPort)
		}

		if len(service.UDPPorts) > 0 {
			udpNode := etree.SubElement(serviceSet, "udp")
			destPort := strings.Join(service.UDPPorts, ",")
			etree.SubElement(udpNode, "dest-port").SetText(destPort)
		}
	}

	return nil
}

func generateRuijieServiceGroups(root *etree.Element, serviceGroups []ServiceGroup) error {
	serviceObjNode := root.FindElement(".//service-obj")
	if serviceObjNode == nil {
		return fmt.Errorf("无法找到 <service-obj> 节点，请检查模板文件")
	}

	for _, group := range serviceGroups {
		serviceGroup := etree.SubElement(serviceObjNode, "service-group")
		etree.SubElement(serviceGroup, "name").SetText(group.Name)

		for _, member := range group.Members {
			serviceSet := etree.SubElement(serviceGroup, "service-set")
			etree.SubElement(serviceSet, "name").SetText(member)
		}
	}

	return nil
}

func generateRuijieAddressGroups(root *etree.Element, addressGroups []FirewallAddrGroup) error {
	networkObjNode := root.FindElement(".//network-obj")
	if networkObjNode == nil {
		return fmt.Errorf("无法找到 <network-obj> 节点，请检查模板文件")
	}

	for _, group := range addressGroups {
		addressGroup := etree.SubElement(networkObjNode, "address-group")
		etree.SubElement(addressGroup, "name").SetText(group.Name)

		for _, member := range group.Members {
			addressSet := etree.SubElement(addressGroup, "address-set")
			etree.SubElement(addressSet, "name").SetText(member)
		}
	}

	return nil
}

func generateRuijieAVConfig(root *etree.Element, avProfiles []AVProfile) error {
	avNode := root.FindElement(".//anti-virus")
	if avNode == nil {
		return fmt.Errorf("无法找到 <anti-virus> 节点，请检查模板文件")
	}

	for _, profile := range avProfiles {
		template := etree.SubElement(avNode, "template")
		etree.SubElement(template, "name").SetText(profile.Name)
		etree.SubElement(template, "description").SetText(profile.Description)
		etree.SubElement(template, "scan-mode").SetText(profile.ScanMode)

		protocolsNode := etree.SubElement(template, "protocols")
		if len(profile.Protocols) == 0 {
			// 默认所有协议
			defaultProtocols := []Protocol{
				{"FTP", "both"}, {"HTTP", "both"}, {"IMAP", "to-client"},
				{"NFS", "both"}, {"POP3", "to-client"}, {"SMB", "both"},
				{"SMTP", "to-server"},
			}
			for _, proto := range defaultProtocols {
				protocolNode := etree.SubElement(protocolsNode, "protocol")
				etree.SubElement(protocolNode, "name").SetText(proto.Name)
				etree.SubElement(protocolNode, "direction").SetText(proto.Direction)
			}
		} else {
			for _, proto := range profile.Protocols {
				protocolNode := etree.SubElement(protocolsNode, "protocol")
				etree.SubElement(protocolNode, "name").SetText(proto.Name)
				etree.SubElement(protocolNode, "direction").SetText(proto.Direction)
			}
		}

		fileTypeSetsNode := etree.SubElement(template, "file-type-sets")
		for _, ft := range profile.FileTypes {
			fileTypeNode := etree.SubElement(fileTypeSetsNode, "file-type")
			etree.SubElement(fileTypeNode, "suffix").SetText(ft)
		}
	}

	return nil
}

func generateRuijieIPSConfig(root *etree.Element, ipsSensors []IPSensor) error {
	ipsNode := root.FindElement(".//ips-config")
	if ipsNode == nil {
		return fmt.Errorf("无法找到 <ips-config> 节点，请检查模板文件")
	}

	severityMapping := map[string]string{
		"critical":      "high",
		"high":          "medium",
		"medium":        "low",
		"low":           "informational",
		"informational": "informational",
	}

	for _, sensor := range ipsSensors {
		template := etree.SubElement(ipsNode, "template")
		etree.SubElement(template, "name").SetText(sensor.Name)
		etree.SubElement(template, "description").SetText(sensor.Comment)

		filterNode := etree.SubElement(template, "filter")
		etree.SubElement(filterNode, "name").SetText("test-filter")
		etree.SubElement(filterNode, "target").SetText("both")

		for _, severity := range sensor.Severity {
			if mapped, ok := severityMapping[strings.ToLower(severity)]; ok {
				etree.SubElement(filterNode, "severity").SetText(mapped)
			}
		}

		protocolNode := etree.SubElement(filterNode, "protocol")
		etree.SubElement(protocolNode, "all-protocol").SetText("true")

		categoryNode := etree.SubElement(filterNode, "category")
		etree.SubElement(categoryNode, "all-category").SetText("true")
	}

	return nil
}

func updateRuijieTemplate(templateFile, outputFile string, firewallAddresses []FirewallAddress, addressGroups []FirewallAddrGroup, policies []Policy, avProfiles []AVProfile, services []Service, serviceGroups []ServiceGroup, ippools []IPPool, ipsSensors []IPSensor) error {
	doc := etree.NewDocument()
	if err := doc.ReadFromFile(templateFile); err != nil {
		return err
	}
	root := doc.Root()

	// IP地址配置
	if err := generateRuijieFirewallAddresses(root, firewallAddresses); err != nil {
		return err
	}

	// 地址组配置
	if err := generateRuijieServiceGroups(root, addressGroups); err != nil {
		return err
	}

	// NAT 配置
	if err := generateRuijieNatConfig(root, policies, ippools); err != nil {
		return err
	}

	// 防病毒配置
	if err := generateRuijieAVConfig(root, avProfiles); err != nil {
		return err
	}

	// 服务配置
	if err := generateRuijieServiceConfig(root, services); err != nil {
		return err
	}

	// 服务组配置
	if err := generateRuijieServiceGroups(root, serviceGroups); err != nil {
		return err
	}

	// IPS 配置
	if err := generateRuijieIPSConfig(root, ipsSensors); err != nil {
		return err
	}

	// 写入输出文件
	return doc.WriteToFile(outputFile)
}

func processFortinetToRuijie(fortinetFile, ruijieTemplate, outputFile string) error {
	file, err := os.Open(fortinetFile)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var lines []string
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	if err := scanner.Err(); err != nil {
		return err
	}

	firewallAddresses := parseFirewallAddress(lines)
	addressGroups := parseFirewallAddrGrp(lines)
	policies := parseFortinetPolicy(lines)
	avProfiles := parseAntiVirus(lines)
	services := parseServiceCustom(lines)
	serviceGroups := parseServiceGroup(lines)
	ippools := parseFirewallIPPool(lines)
	ipsSensors := parseIPSSensor(lines)

	fmt.Println(ipsSensors)

	return updateRuijieTemplate(ruijieTemplate, outputFile, firewallAddresses, addressGroups, policies, avProfiles, services, serviceGroups, ippools, ipsSensors)
}

func main() {
	fortinetConfigPath := "E:\\fortinet_config.conf"
	ruijieTemplatePath := "E:\\Z8620-startup-R10.xml"
	outputPath := "updated_ruijie_template.xml"

	if err := processFortinetToRuijie(fortinetConfigPath, ruijieTemplatePath, outputPath); err != nil {
		fmt.Printf("转换失败: %v\n", err)
	} else {
		fmt.Println("转换成功，输出文件为:", outputPath)
	}
}