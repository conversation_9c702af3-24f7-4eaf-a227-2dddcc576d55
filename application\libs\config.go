package libs

import (
	"fmt"
	"path/filepath"
	"strings"

	"irisAdminApi/application/libs/easygorm"

	"github.com/jinzhu/configor"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

var Config = struct {
	LogLevel     string `env:"Loglevel" default:"info"`
	Host         string `env:"Host" default:"127.0.0.1" `
	Port         int64  `env:"Port" default:"80"`
	ReadTimeout  int64  `env:"ReadTimeout" default:"60"`
	WriteTimeout int64  `env:"WriteTimeout" default:"60"`
	MaxSize      int64  `env:"MaxSize" default:""`
	Pprof        bool   `env:"Pprof" default:"false"`
	Debug        bool   `env:"Debug" default:"false"`
	Ddebug       bool   `env:"Ddebug" default:"false"`
	SSH          struct {
		Host     string `env:"SSHHost" default:""`
		User     string `env:"SSHUser" default:""`
		Password string `env:"SSHPassword" default:""`
	}
	Nginx struct {
		HOST string `env:"Host" default:"127.0.0.1" `
		Port int64  `env:"Port" default:"80"`
		Path string `env:"Path" default:"/api"`
	}
	Cache struct {
		Driver string `env:"CacheDriver" default:"local"`
	}
	Casbin struct {
		Path    string `env:"CasbinPath" default:"" `
		Prefix  string `env:"CasbinPrefix" default:"casbin" `
		Version int    `default:"0"`
	}
	Admin struct {
		Username string `env:"AdminUsername" default:""`
		Name     string `env:"AdminName" default:""`
		Password string `env:"AdminPassword" default:""`
		Rolename string `env:"AdminRolename" default:""`
	}
	DB struct {
		Prefix  string `env:"DBPrefix" default:""`
		Name    string `env:"DBName" default:"blog"`
		Adapter string `env:"DBAdapter" default:"mysql"`
		Conn    string `env:"DBConn" default:"root:password@tcp(localhost:3306)/iris?parseTime=True&loc=Local"`
		Encrypt bool   `env:"RedisEncrypt" default:"false"`
	}
	Redis struct {
		Host     string `env:"RedisHost" default:"localhost"`
		Port     string `env:"RedisPort" default:"6379"`
		Password string `env:"RedisPassword" default:""`
		Encrypt  bool   `env:"RedisEncrypt" default:"false"`
	}
	Limit struct {
		Disable bool    `env:"LimitDisable" default:"true"`
		Limit   float64 `env:"LimitLimit" default:"1"`
		Burst   int     `env:"LimitBurst" default:"5"`
	}
	Qiniu struct {
		Enable    bool   `env:"QiniuEnable" default:"false"`
		Host      string `env:"QiniuHost" default:""`
		Accesskey string `env:"QiniuAccesskey" default:""`
		Secretkey string `env:"QiniuSecretkey" default:""`
		Bucket    string `env:"QiniuBucket" default:""`
	}
	FileStorage struct {
		Temp   string `env:"" default:""`
		Upload string `env:"" default:""`
	}
	// Objdump struct {
	// 	Path string `env:"ObjdumpPath" default:""`
	// }
	// FileCommand struct {
	// 	Path string `env:"ObjdumpPath" default:""`
	// }
	Rsa struct {
		Publickey  string `default:""`
		Privatekey string `default:""`
	}

	FileOut struct {
		Enable bool `default:"false"`
	}

	Gitlab struct {
		Url        string `default:""`
		Version    string `default:""`
		Token      string `default:""`
		Enable     bool   `default:"false"`
		IsInternal bool   `default:"true"`
		AiToken    string `default:""`
	}

	Buildfarm struct {
		CronMailReceivers string `default:""`
		Logpath           string `default:"/tmp/"`
		Compilepath       string `default:"/tmp/"`
		Archivepath       string `default:"/tmp/"`
		Releasepath       string `default:"/tmp/"`
		Temp              string `default:"/tmp/"`
		User              string `default:""`
		Token             string `default:""`
		Enable            bool   `default:"false"`
		BaseosArchive     struct {
			User     string `default:""`
			Host     string `default:""`
			Port     string `default:""`
			Password string `default:""`
			Dir      string `default:""`
			Enable   bool   `default:"false"`
		}
		Jenkinsurl       string `default:""`
		Jenkinsuser      string `default:""`
		Jenkinspass      string `default:""`
		CheckPersonalGit struct {
			WorkDir string `default:""`
		}
		Cpld struct {
			Enable bool   `default:"false"`
			Upload string `default:"/tmp/cpld/upload"`
			Output string `default:"/tmp/cpld/output"`
		}
	}

	Release struct {
		Enable      bool   `default:"false"`
		Releasepath string `default:"/tmp/"`
		Host        string `default:"请联系管理员添加发布服务器主机地址"`
		DPRSYSID    string `default:""`
		GcovServer  string `default:"************"`
	}

	FeatureRelease struct {
		Upload   string `default:"/tmp/"`
		Url      string `default:""`
		Enable   bool   `default:"false"`
		BIApiUrl string `default:""`
	}

	CheckCCode struct {
		Type       string `default:"python3"`
		Script     string `default:""`
		Repository string `default:""`
		Threshold  string `default:"0.5"`
	}

	Mail struct {
		Enable bool   `defaut:"false"`
		Queue  string `default:"mail"`
	}

	PatchFileStorage struct {
		Temp         string `default:"/tmp/temp/"`
		Upload       string `default:"/tmp/upload/patch/"`
		CompilePath  string `default:"/tmp/compile/"`
		ArchivePath  string `default:"/tmp/compileoutput/"`
		Gitrepo      string `default:""`
		HotpatchRepo string `default:""`
		Branch       string `default:"master"`
		NewBranch    string `default:"Trunk"`
	}

	DetectionFileStorage struct {
		Temp       string `default:"/tmp/temp/"`
		Upload     string `default:"/tmp/upload/detection/"`
		ToolPath   string `default:"/root/main.dist/"`
		ToolName   string `default:"main"`
		OutputPath string `default:"/tmp/upload/detection/output/"`
		LogPath    string `default:"/tmp/upload/detection/log/"`
		EnvConfig  string `default:""`
	}

	InfoWorkFileWatch struct {
		SDKPath             string `default:""`
		HashPath            string `default:""`
		SDKWorkSuccessFile  string `default:""`
		HashworkSuccessFile string `default:""`
		WaitingTime         int    `default:"0"`
	}

	BugSync struct {
		Enable bool `default:"false"`
	}

	Feature struct {
		Enable bool `default:"false"`
	}

	CodeSync struct {
		Enable bool   `default:"false"`
		Token  string `default:""`
		Mail   bool   `default:"false"`
	}

	CoreDump struct {
		Upload        string `default:""`
		Output        string `default:""`
		Host          string `default:""`
		Port          string `default:""`
		Username      string `default:""`
		RemoteWorkDir string `default:""`
		Plugin        string `default:""` // 脚本目录
		DecryptedDir  string `default:"/mnt/sata0/coredump/"`
		PathHeader    string `default:""`
		Enable        bool   `default:"false"`
	}

	UrlPack struct {
		Outputs     string `default:""`
		Logs        string `default:""`
		Program     string `default:"txt2bloom"`
		Sigpath     string `default:""`
		ExactDir    string `default:""`
		OverseasDir string `default:""`
	}

	OpenSource struct {
		Enable                          bool `default:"false"`
		CVESyncTaskWorkers              int  `default:"1"`
		SendEmailTaskWorkers            int  `default:"1"`
		TempCheckTaskWorkers            int  `default:"1"`
		CVESyncIntervalInHours          int  `default:"168"` // 168 hours = 7 days
		VulnerabilityDelayedTimeInHours int  `default:"168"` // 168 hours = 7 days
		CNVDSyncIntervalInHours         int  `default:"168"` // 168 hours = 7 days
	}
	MergeRequest struct {
		Enable           bool   `default:"false"`
		PhabricatorToken string `default:""`
		PhabricatorUrl   string `default:""`
		CodeCheck        string `default:""`
		CodeCheckEnable  bool   `default:"true"`
		AICheckEnable    bool   `default:"true"`
		OpenSource       struct {
			Host     string `default:""`
			Port     int    `default:"22"`
			Username string `default:""`
			Password string `default:""`
		}
		BugCheckEnable  bool `default:"true"`
		YangCheckEnable bool `default:"true"`
	}

	AppMergeRequest struct {
		Enable          bool `default:"false"`
		AICheckEnable   bool `default:"false"`
		BugCheckEnable  bool `default:"false"`
		YangCheckEnable bool `default:"false"`
	}

	License struct {
		Enable                bool   `default:"false"`
		DeviceAuthTaskWorkers int    `default:"1"`
		AdminUsername         string `default:"admin_auto_authorization"`
		AdminPassword         string `default:"U0WlP2tXWkinYw+Ecc2+vg=="`
		AdminEndpoint         string `default:"https://**************"`
		SecCloudEndpoint      string `default:"https://secloud0.ruijie.com.cn"`
		ProductionEndpoint    string `default:"http://************:4011"`
		AuthCodeEndpoint      string `default:"https://**************"`
		Username              string `default:"automation"`
		Password              string `default:"uvgyhE0wcT4ZvmEwOPlqAg=="`
		BasicAuthUsername     string `default:"security"`
		BasicAuthPassword     string `default:"Ruijie@123456"`
	}
	Resourcepool struct {
		Enable bool `default:"false"`
		Ssh    struct {
			Host     string `default:""`
			Port     string `default:"22"`
			Username string `default:""`
			Password string `default:"password"`
			Psw      string `default:"/volumes/lib/labtools/ui_psw"`
		}
		Testbed string `default:""`
	}
	Performance struct {
		Enable bool   `default:"false"`
		Dfx    string `default:"process,hm-pool,dynamic-pool,static-pool,mbuf-pool"`
	}

	ProductionFileStorage struct {
		Temp       string `default:"/tmp/temp/"`
		Upload     string `default:"/tmp/upload/production/doctemplates/"`
		OutputPath string `default:"/tmp/upload/production/output/"`
		LogPath    string `default:"/tmp/upload/production/log/"`
		SVNUser    string `default:""`
		SVNPass    string `default:""`
		Enable     bool   `default:"false"`
		Plugin     string `default:""`
	}
	Sig struct {
		Enable                  bool   `default:"false"`
		Upload                  string `default:"/tmp/input"`
		Sigpath                 string `default:""`
		Techsupportdecryptpath  string `default:""`
		DpdinfoPath             string `default:""`
		App                     string `default:""`
		RunningConfigServer     string `default:"127.0.0.1"`
		RunningConfigUser       string `default:"root"`
		RunningConfigPass       string `default:"password"`
		RunningConfigRemotePath string `default:"/tmp/running-config-decrypt"`
		RunningConfigPort       int    `default:"22"`
		PerfServer              string `default:"127.0.0.1"`
		PerfUser                string `default:"root"`
		PerfPass                string `default:"password"`
		PerfPort                int    `default:"22"`
		PerfRemotePath          string `default:"/tmp/perf-analysis"`
	}

	DataSync struct {
		Enable bool `default:"false"`
		Svn    struct {
			Username   string `default:""`
			Password   string `default:""`
			OutputPath string `default:"/tmp"`
		}
		Influxdb struct {
			Url    string `default:""`
			Token  string `default:""`
			Org    string `default:""`
			Bucket string `default:""`
		}
		PmsUser string `default:""`
		PmsPass string `default:""`
	}
	AccessLog struct {
		Enable  bool   `default:"false"`
		Sites   string `default:""`
		LogPath string `default:"/tmp/accesslog/"`
	}
	FeiShuDoc struct {
		AppID       string `default:""`
		AppSecret   string `default:""`
		Enable      bool   `default:"false"`
		FolderToken string `default:""`
		BiAppToken  string `default:""`
		PmsUser     string `default:""`
		PmsPass     string `default:""`
		Plugin      string `default:""` // 脚本目录
		Temp        string `default:"/tmp/temp/"`
		OutputPath  string `default:"/tmp/upload/feishu/"` // 输出目录
		Template    string `default:"/tmp/upload/feishu/"` // 模板目录
		// 品控系统配置
		// QualityControlURL: 品控系统基础URL，用于构建SSO登录地址
		// SSO登录将使用: https://sid.ruijie.com.cn/login?service={QualityControlURL}%2Fshiro-cas
		QualityControlURL      string `default:"http://yfzl.ruijie.com.cn"`
		QualityControlAppToken string `default:"Lmv4bBg5jaJ60rsueK7cEywqnzc"`
		QualityControlTableID  string `default:"tblhcAFqlKmQc4Hi"`
	}
	CoveritySync struct {
		Enable          bool   `default:"false"`
		Url             string `default:""`
		EpgRuleFileName string `default:"rule.json"`
	}
	Gcov struct {
		Enable bool `default:"false"`
	}
	Kpi struct {
		Eanble bool `default:"false"`
	}
	QualityPoint struct {
		Enable           bool   `default:"false"`
		Upload           string `default:"/tmp/upload/qualityviolation/"`
		TrialRun         bool   `default:"true"`
		QualityManagerID string `default:"261"`
		ScoreAdminID     string `default:"75"`
	}
	DocumentAuto struct {
		MaxFileSize      int64  `default:"5242880"` // 5MB
		SavePath         string `default:"/tmp/upload/documentauto/"`
		TempPath         string `default:"/tmp/temp/documentauto/"`
		WordTemplatePath string `default:"/tmp/upload/documentauto/word_templates/"`
		ScreenshotPath   string `default:"/tmp/upload/documentauto/screenshots/"`
		Plugin           string `default:""` // 脚本目录
		Server           string `default:"http://192.168.56.101:9003"`
		DownloadServer   string `default:"http://************:9090"`
	}
}{}

func InitConfig(config string) error {
	path := filepath.Join(CWD(), "application.yml")
	if config != "" {
		path = config
	}

	if err := configor.Load(&Config, path); err != nil {
		return err
	}

	if Config.Casbin.Path == "" {
		Config.Casbin.Path = filepath.Join(CWD(), "rbac_model.conf")
	}

	if Config.MaxSize == 0 {
		Config.MaxSize = 5 << 20
	}

	return nil
}

// redis 集群
func GetRedisUris() []string {
	addrs := make([]string, 0, 0)
	hosts := strings.Split(Config.Redis.Host, ";")
	ports := strings.Split(Config.Redis.Port, ";")
	for _, h := range hosts {
		for _, p := range ports {
			addrs = append(addrs, fmt.Sprintf("%s:%s", h, p))
		}
	}
	return addrs
}

func GetGormConfig() *easygorm.Config {
	c := &easygorm.Config{
		Adapter: Config.DB.Adapter,
		Conn:    GetDBConn(),
		GormConfig: &gorm.Config{
			NamingStrategy: schema.NamingStrategy{
				TablePrefix: Config.DB.Prefix,
			},
		},
		Casbin: &easygorm.Casbin{
			Path:   Config.Casbin.Path,
			Prefix: Config.Casbin.Prefix,
		},
	}
	// if Config.DB.Prefix != "" {
	// 	c.GormConfig = &gorm.Config{NamingStrategy: schema.NamingStrategy{TablePrefix: Config.DB.Prefix}}
	// }
	return c
}

func GetDBConn() string {
	if Config.DB.Encrypt {
		conn, err := RsaDecrypt(Config.DB.Conn)
		if err != nil {
			return ""
		}
		return string(conn)
	}
	return Config.DB.Conn
}
