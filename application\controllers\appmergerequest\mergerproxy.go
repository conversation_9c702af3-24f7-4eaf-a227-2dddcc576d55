package appmergerequest

import (
	"bytes"
	"compress/gzip"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"irisAdminApi/application/libs"
	"irisAdminApi/application/libs/easygorm"
	"irisAdminApi/application/libs/response"
	"irisAdminApi/application/logging"
	"irisAdminApi/application/models"
	"irisAdminApi/service/dao"
	buildfarmProject "irisAdminApi/service/dao/buildfarm/dproject"
	"irisAdminApi/service/dao/datasync/dbug"
	"irisAdminApi/service/dao/mergerequest/dmergerequesbuttonclicklog"
	"irisAdminApi/service/dao/mergerequest/dmergerequescontrolreview"
	"irisAdminApi/service/dao/mergerequest/dmergerequesdependencies"
	"irisAdminApi/service/dao/mergerequest/dmergerequest"
	"irisAdminApi/service/dao/mergerequest/dmergerequestworkpackage"
	"irisAdminApi/service/dao/release/dbranch"
	dreleaseproject "irisAdminApi/service/dao/release/dproject"
	"irisAdminApi/service/dao/user/dgitlabtoken"
	"irisAdminApi/service/dao/user/duser"

	"github.com/kataras/iris/v12"
	"github.com/xanzy/go-gitlab"
)

func MergeHandler(ctx iris.Context) {
	data, _ := io.ReadAll(ctx.Request().Body)
	headers := ctx.Request().Header
	referer := headers.Get("Referer")                 // 来源链接
	gitlabSession := ctx.GetCookie("_gitlab_session") // 获取 GitLab 登录会话
	csrfToken := headers.Get("X-Csrf-Token")
	// 解析 JSON 数据
	var postMergeData Merge
	if err := json.Unmarshal(data, &postMergeData); err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error unmarshaling JSON:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	url := libs.Config.Gitlab.Url + "/"                // libs.Config.Gitlab.Url
	userName, _ := getGitlabUserInfoByCookie(url, ctx) //
	// 通过用户名获取gitlab私有token

	privateToken, userID, err := GetGitlabTokenByUserName(userName)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting Gitlab token:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// privateToken := libs.Config.Buildfarm.Token
	// 通过 token 获取 gitlab client
	gl, err := gitlab.NewClient(privateToken, gitlab.WithBaseURL(url))
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error creating Gitlab client:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 尝试获取当前用户的信息验证Token是否正常
	user, _, err := gl.Users.CurrentUser()
	if err != nil {
		logging.ErrorLogger.Errorf("Token验证失败: ", err)
		// 重新生成用户Token
		privateToken, err = CreatGitlabTokenByUserName(userName)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("Error creating Gitlab client:", err)
			ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
			return
		}
		// 创建新的GitLab客户端
		gl, err = gitlab.NewClient(privateToken, gitlab.WithBaseURL(url))
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("Error creating Gitlab client:", err)
			ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
			return
		}
		user, _, _ = gl.Users.CurrentUser()
	}
	fmt.Println(2222, user.Username)
	// 获取 IID 和项目路径
	pathWithNamespace, iidStr, err := parseReferer(referer)
	if err != nil {
		ctx.StatusCode(iris.StatusBadRequest)
		logging.ErrorLogger.Errorf("Error parsing referer:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 将字符串 IID 转换为整型
	iid, err := strconv.Atoi(iidStr)
	if err != nil {
		ctx.StatusCode(iris.StatusBadRequest)
		logging.ErrorLogger.Errorf("Error converting IID to int: ", err)
		ctx.JSON(iris.Map{"status": "failed", "error": "Invalid IID"})
		return
	}
	// 使用Gitlab项目路径和命名空间获取项目
	gitlabProject, _, err := gl.Projects.GetProject(pathWithNamespace, &gitlab.GetProjectOptions{})
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting Gitlab project:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}

	// 获取 gitlab-merge request详情
	gitlabMergeRequest, _, err := gl.MergeRequests.GetMergeRequest(gitlabProject.ID, iid, &gitlab.GetMergeRequestsOptions{})
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting Gitlab merge request:", err)
		ctx.JSON(iris.Map{"status": "failed", "error2": err.Error()})
		return
	}
	// 获取merge request数据
	mergeRequest, err := dmergerequest.GetMergeRequestByMRIDAndTargetProjectID(gitlabMergeRequest.ID, gitlabProject.ID)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting merge request:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	if mergeRequest.ID == 0 {
		// 判断是否属于管控项目
		buildFarmProject := buildfarmProject.Response{}
		if err := buildFarmProject.FindEx("gitlab_id", fmt.Sprintf("%d", gitlabProject.ID)); err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("Error getting buildfarm project查找gitlab仓库失败", err)
			ctx.JSON(iris.Map{"status": "failed", "error": "查找gitlab仓库失败"})
			return
		}
		if buildFarmProject.Id > 0 {
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("获取MergeRequestID失败，请联系系统管理员", err)
			ctx.JSON(iris.Map{"status": "failed", "error": "获取MergeRequestID失败，请联系系统管理员"})
			return
		}
		// MR合并请求
		sha := gitlabMergeRequest.SHA
		acceptMergeRequest, resp, err := gl.MergeRequests.AcceptMergeRequest(int(gitlabProject.ID), iid, &gitlab.AcceptMergeRequestOptions{SHA: &sha})
		if err != nil {
			// ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("API方式-MR合并请求失败:", err.Error())
			_, err := MergeMergeRequestByGitlabSession(iid, gitlabSession, csrfToken, gitlabProject.PathWithNamespace, gitlabMergeRequest.SHA)
			if err != nil {
				ctx.StatusCode(iris.StatusInternalServerError)
				logging.ErrorLogger.Errorf("Session请求方式-MR合并请求失败:", err.Error())
				ctx.JSON(iris.Map{"status": "failed", "error6": err.Error()})
				return
			} else {
				ctx.StatusCode(iris.StatusOK)
				ctx.JSON(iris.Map{"status": "success", "error": "none"})
				return
			}
		}
		if acceptMergeRequest == nil {
			// 通过token请求
			_, err := MergeMergeRequestByGitlabSession(iid, gitlabSession, csrfToken, gitlabProject.PathWithNamespace, gitlabMergeRequest.SHA)
			if err != nil {
				ctx.StatusCode(iris.StatusInternalServerError)
				logging.ErrorLogger.Errorf("Session请求方式-MR合并请求失败:", err.Error())
				ctx.JSON(iris.Map{"status": "failed", "error7": err.Error()})
				return
			} else {
				ctx.StatusCode(iris.StatusOK)
				ctx.JSON(iris.Map{"status": "success", "error": "none"})
				return
			}
		}
		if acceptMergeRequest.State == "opened" {
			// 通过token请求
			_, err := MergeMergeRequestByGitlabSession(iid, gitlabSession, csrfToken, gitlabProject.PathWithNamespace, gitlabMergeRequest.SHA)
			if err != nil {
				ctx.StatusCode(iris.StatusInternalServerError)
				logging.ErrorLogger.Errorf("Session请求方式-MR合并请求失败:", err.Error())
				ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
				return
			} else {
				ctx.StatusCode(iris.StatusOK)
				ctx.JSON(iris.Map{"status": "success", "error": "none"})
				return
			}
		}
		fmt.Println(resp)
		ctx.StatusCode(iris.StatusOK)
		ctx.JSON(iris.Map{"status": "success", "error": "none"})
		return
	}
	// 获取项目数据
	project := dreleaseproject.Response{}
	err = project.Find(mergeRequest.ReleaseProjectID)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting project:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 验证 工作包状态==>分支状态==>项目状态   0开启 1关闭 2管控
	mrControlStatus := 0
	if project.ID > 0 {
		// SP分支默认放开 TargetBranch 以"_sp" 包含大小写 结尾的分支
		if strings.Contains(strings.ToLower(mergeRequest.TargetBranch), "_sp") {
			mrControlStatus = 0
		} else {
			// 项目存在验证工作包状态
			workpackage, err := dmergerequestworkpackage.FindIDAndReleaseProjectID(mergeRequest.WorkPackageID, mergeRequest.ReleaseProjectID)
			if err != nil {
				logging.ErrorLogger.Errorf("Error getting FindIDAndReleaseProjectID:", err)
				ctx.StatusCode(iris.StatusInternalServerError)
				ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
				return
			}
			if workpackage.ID > 0 {
				mrControlStatus = int(workpackage.MrControlStatus)
			} else {
				// 工作包不存在，使用分支状态
				branch, err := dbranch.GetBranchByReleaseProjectID(mergeRequest.ReleaseProjectID, mergeRequest.TargetBranch)
				if err != nil {
					ctx.StatusCode(iris.StatusInternalServerError)
					ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
					return
				}
				if branch.ID > 0 {
					mrControlStatus = int(branch.MrControlStatus)
				} else {
					mrControlStatus = int(project.MrControlStatus)
				}
			}
		}
	}
	if mrControlStatus == 1 {
		ctx.StatusCode(iris.StatusOK)
		logging.ErrorLogger.Errorf("当前项目/分支/工作包，已关闭合并，请联系项目管理员")
		ctx.JSON(iris.Map{"status": "failed", "error": "当前项目/分支/工作包，已关闭合并，请联系项目管理员"})
		return
	}
	if mergeRequest.ButtonClickStatus == 1 {
		ctx.StatusCode(iris.StatusOK)
		logging.ErrorLogger.Errorf("当前MR合并按钮已点击过，请联系项目管理员")
		ctx.JSON(iris.Map{"status": "failed", "error": "当前MR合并按钮已点击过，请联系项目管理员"})
		return
	}
	// 点击按钮记录操作
	buttonclicklogObjects := dmergerequesbuttonclicklog.Response{}
	buttonclicklogObjects.CreatedAt = time.Now()
	buttonclicklogObjects.MergeRequestID = mergeRequest.ID
	buttonclicklogObjects.ReleaseProjectID = mergeRequest.ReleaseProjectID
	buttonclicklogObjects.ReleaseProject = mergeRequest.ReleaseProject
	buttonclicklogObjects.WorkPackageID = mergeRequest.WorkPackageID
	buttonclicklogObjects.UnitPackage = mergeRequest.TargetProjectID
	buttonclicklogObjects.UserID = userID
	dmergerequesbuttonclicklog := dmergerequesbuttonclicklog.Response{}
	err = dmergerequesbuttonclicklog.CreateV3(&buttonclicklogObjects)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error creating buttonclicklog:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	if mergeRequest.ButtonClickStatus == 0 {
		// 更新MR表单点击按钮状态以及sha值
		mergerequestObjects := map[string]interface{}{
			"ButtonClickStatus": 1,
			"ButtonClickUser":   userName,
			"MergeRequestSHA":   gitlabMergeRequest.SHA,
		}
		mergeRequestUp := dmergerequest.MergeRequest{}
		err = mergeRequestUp.Update(mergeRequest.ID, mergerequestObjects)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("Error updating mergerequest:", err)
			ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
			return
		}
	}
	// 管控状态下生成审核单
	if mrControlStatus == 2 {
		// 查找审核单是否存在
		controlReviews, err := dmergerequescontrolreview.FindByMergeRequestID(mergeRequest.ID, mergeRequest.ReleaseProjectID, mergeRequest.WorkPackageID)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("Error getting controlReviews:", err)
			ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		}
		if controlReviews.ID > 0 {
			ctx.StatusCode(iris.StatusOK)
			logging.ErrorLogger.Errorf("审核单已存在，请联系项目管理员")
			ctx.JSON(iris.Map{"status": "failed", "error": "审核单已存在，请联系项目管理员"})
			return
		}
		// todo: 增加判断AI检查是否完成,如未完成，则取消，等待取消成功后，检查是否存在AI评审未关闭
		cancelFuncValue, ok := CodeCheckWorkerRunning.Load(mergeRequest.ID)
		if ok {
			// 检查类型是否为 context.CancelFunc
			if cancelFunc, valid := cancelFuncValue.(context.CancelFunc); valid {
				cancelFunc()
			} else {
				ctx.StatusCode(iris.StatusInternalServerError)
				ctx.JSON(iris.Map{"status": "error", "error": "无法取消AI代码评审作业，类型不匹配。"})
				return
			}
		}

		// 使用更灵活的等待机制，带超时
		const maxRetries = 10
		const retryInterval = time.Second * 1
		cancelCompleted := false

		for range maxRetries {
			_, ok := CodeCheckWorkerRunning.Load(mergeRequest.ID)
			if !ok {
				cancelCompleted = true
				break
			}
			time.Sleep(retryInterval)
		}

		// 如果取消未完成，返回错误信息
		if !cancelCompleted {
			ctx.StatusCode(iris.StatusOK)
			ctx.JSON(iris.Map{"status": "failed", "error": "存在进行中AI代码评审作业，已经取消，请刷新后重试。"})
			return
		}

		// 获取审核单数据
		controlReviewsObjects := map[string]interface{}{
			"CreatedAt":        time.Now(),
			"UpdatedAt":        time.Now(),
			"MergeRequestID":   mergeRequest.ID,
			"ReleaseProjectID": mergeRequest.ReleaseProjectID,
			"ReleaseProject":   mergeRequest.ReleaseProject,
			"WorkPackageID":    mergeRequest.WorkPackageID,
			"UnitPackage":      mergeRequest.TargetProjectID,
			"UserID":           userID,
			"UserName":         userName,
			"Status":           0,
			"Uuid":             libs.GetUUID(),
		}
		// 插入到MR审核数据库中
		err = easygorm.GetEasyGormDb().Model(dmergerequescontrolreview.Model()).Create(controlReviewsObjects).Error
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("Error creating controlReviews:", err)
			ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
			return
		}
		ctx.StatusCode(iris.StatusOK)
		go SendMailToPM(mergeRequest)
		ctx.JSON(iris.Map{"status": "failed", "error": "审核单已生成，请联系PM进行审核处理"})
		return
	}

	// 判断是否存在依赖项目
	dependencyStatus := false
	if len(mergeRequest.Dependencies) > 0 {
		for _, dependency := range mergeRequest.Dependencies {
			if dependency.Status != 3 {
				dependencyStatus = true
				break
			}
		}
	}
	if dependencyStatus {
		ctx.StatusCode(iris.StatusOK)
		logging.ErrorLogger.Errorf("存在依赖项目未合并请耐心等待.....")
		ctx.JSON(iris.Map{"status": "failed", "error": "存在依赖项目未合并请耐心等待....."})
		return
	}

	// MR合并请求
	sha := gitlabMergeRequest.SHA
	acceptMergeRequest, resp, err := gl.MergeRequests.AcceptMergeRequest(int(mergeRequest.TargetProjectID), iid, &gitlab.AcceptMergeRequestOptions{SHA: &sha})
	logEntry := LogEntry{
		Status:  resp.Status,
		Headers: resp.Header,
	}
	if err != nil {
		logging.ErrorLogger.Errorf("MR合并请求失败: %v", err.Error())
		logEntry.Error = err.Error()
		if resp.StatusCode != 200 {
			if resp.StatusCode == 403 {
				// 如果是因为权限不足导致的错误，resp.StatusCode 会是 403 Forbidden
				// 重新生成用户Token
				privateToken, err = CreatGitlabTokenByUserName(userName)
				if err != nil {
					ctx.StatusCode(iris.StatusInternalServerError)
					logging.ErrorLogger.Errorf("Error creating Gitlab client:", err)
					ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
				}
				// 创建新的GitLab客户端
				gl, err = gitlab.NewClient(privateToken, gitlab.WithBaseURL(url))
				if err != nil {
					ctx.StatusCode(iris.StatusInternalServerError)
					logging.ErrorLogger.Errorf("Error creating Gitlab client:", err)
					ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
				}
				// 开始重新合并
				_, resp2, err := gl.MergeRequests.AcceptMergeRequest(int(mergeRequest.TargetProjectID), iid, &gitlab.AcceptMergeRequestOptions{SHA: &sha})
				logEntry.Status = resp2.Status
				logEntry.Headers = resp2.Header
				if err != nil {
					logEntry.Error = err.Error()
					logging.ErrorLogger.Errorf("MR合并请求失败: %v", err.Error())
				}
			} else {
				ctx.JSON(iris.Map{"status": "failed"})
			}
		} else {
			resp3, err := MergeMergeRequestByGitlabSession(iid, gitlabSession, csrfToken, gitlabProject.PathWithNamespace, gitlabMergeRequest.SHA)
			logEntry.Error = "Session请求方式返回结果:" + resp3
			if err != nil {
				mergeRequestUp := dmergerequest.MergeRequest{}
				UpErr := mergeRequestUp.Update(mergeRequest.ID, map[string]interface{}{"ButtonClickStatus": 0})
				if UpErr != nil {
					logging.ErrorLogger.Errorf("ButtonClickStatus更新失败:", UpErr.Error())
				}
				ctx.StatusCode(iris.StatusInternalServerError)
				logging.ErrorLogger.Errorf("Session请求方式-MR合并请求失败:", err.Error())
				ctx.JSON(iris.Map{"status": "failed", "error6": err.Error()})

			} else {
				ctx.StatusCode(iris.StatusOK)
				ctx.JSON(iris.Map{"status": "success", "error": "none"})
			}
		}
	}

	if acceptMergeRequest == nil {
		// 通过token请求
		resp, err := MergeMergeRequestByGitlabSession(iid, gitlabSession, csrfToken, gitlabProject.PathWithNamespace, gitlabMergeRequest.SHA)
		logEntry.Error = "Session请求方式返回结果:" + resp
		if err != nil {
			mergeRequestUp := dmergerequest.MergeRequest{}
			UpErr := mergeRequestUp.Update(mergeRequest.ID, map[string]interface{}{"ButtonClickStatus": 0})
			if UpErr != nil {
				logging.ErrorLogger.Errorf("ButtonClickStatus更新失败:", UpErr.Error())
			}
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("Session请求方式-MR合并请求失败:", err.Error())
			ctx.JSON(iris.Map{"status": "failed", "error7": err.Error()})

		} else {
			ctx.StatusCode(iris.StatusOK)
			ctx.JSON(iris.Map{"status": "success", "error": "none"})
		}
	}
	if acceptMergeRequest != nil && acceptMergeRequest.State == "opened" {
		// 通过token请求
		resp, err := MergeMergeRequestByGitlabSession(iid, gitlabSession, csrfToken, gitlabProject.PathWithNamespace, gitlabMergeRequest.SHA)
		logEntry.Error = "Session请求方式返回结果:" + resp
		if err != nil {
			mergeRequestUp := dmergerequest.MergeRequest{}
			UpErr := mergeRequestUp.Update(mergeRequest.ID, map[string]interface{}{"ButtonClickStatus": 0})
			if UpErr != nil {
				logging.ErrorLogger.Errorf("ButtonClickStatus更新失败:", UpErr.Error())
			}
			ctx.StatusCode(iris.StatusInternalServerError)
			logging.ErrorLogger.Errorf("Session请求方式-MR合并请求失败:", err.Error())
			ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		} else {
			ctx.StatusCode(iris.StatusOK)
			ctx.JSON(iris.Map{"status": "success", "error": "none"})
		}
	}
	err = UpdateButtonClickLog(logEntry, buttonclicklogObjects)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error updating buttonclicklog.:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
	}
	// 获取MR最新信息
	gitlabMergeRequestNew, _, err := gl.MergeRequests.GetMergeRequest(int(mergeRequest.TargetProjectID), iid, &gitlab.GetMergeRequestsOptions{})
	if err != nil {
		logging.ErrorLogger.Errorf("重新获取MR信息失败:", err.Error())
	}
	// 如果状态还是opened，则重置ButtonClickStatus
	if gitlabMergeRequestNew.State == "opened" {
		mergeRequestUp := dmergerequest.MergeRequest{}
		UpErr := mergeRequestUp.Update(mergeRequest.ID, map[string]interface{}{"ButtonClickStatus": 0})
		if UpErr != nil {
			logging.ErrorLogger.Errorf("ButtonClickStatus更新失败:", UpErr.Error())
		}
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// fmt.Println(resp)
	// 被依赖项操作-->符合条件的被依赖项都完成合并操作
	go MergeDependentMRs(mergeRequest)
	ctx.StatusCode(iris.StatusOK)
	ctx.JSON(iris.Map{"status": "success", "error": "none"})
}

func CachedWidgetHandler(ctx iris.Context) {
	headers := ctx.Request().Header
	referer := headers.Get("Referer") // 来源链接
	url, ret, iid, err := GitLabProxyRequest(ctx, 1)
	if err != nil {
		logging.ErrorLogger.Errorf("Error GitLabProxyRequest", url, err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 获取 IID 和项目路径
	pathWithNamespace, _, err := parseReferer(referer)
	if err != nil {
		ctx.StatusCode(iris.StatusBadRequest)
		logging.ErrorLogger.Errorf("Error parseReferer:", err, referer)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}

	privateToken := libs.Config.Buildfarm.Token
	// 通过 token 获取 gitlab client
	gl, err := gitlab.NewClient(privateToken, gitlab.WithBaseURL(libs.Config.Gitlab.Url))
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting merge request:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 使用Gitlab项目路径和命名空间获取项目
	gitlabProject, _, err := gl.Projects.GetProject(pathWithNamespace, &gitlab.GetProjectOptions{})
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting GetProject:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}

	// 获取 gitlab-merge request详情
	gitlabMergeRequest, _, err := gl.MergeRequests.GetMergeRequest(gitlabProject.ID, iid, &gitlab.GetMergeRequestsOptions{})
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting GetMergeRequest:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}

	// 获取merge request数据
	mergeRequest := dmergerequest.MergeRequest{}
	if err := easygorm.GetEasyGormDb().Model(dmergerequest.Model()).Where("merge_request_id = ? and target_project_id= ?", gitlabMergeRequest.ID, gitlabProject.ID).Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies").Find(&mergeRequest).Error; err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting GetMergeRequest:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 不存在的mergerequest 原样输出
	if mergeRequest.ID == 0 {
		ctx.StatusCode(iris.StatusOK)
		ctx.Header("Poll-Interval", "5000")
		ctx.ContentType("application/json")
		ctx.JSON(ret)
		return
	}

	// 判断mergerequest 状态为-1、3、4原样输出
	if mergeRequest.ID > 0 {
		if mergeRequest.Status == -1 || mergeRequest.Status == 3 || mergeRequest.Status == 4 {
			ctx.StatusCode(iris.StatusOK)
			ctx.Header("Poll-Interval", "5000")
			ctx.ContentType("application/json")
			ctx.JSON(ret)
			return
		}
		if gitlabMergeRequest.State == "merged" || gitlabMergeRequest.State == "closed" {
			ctx.StatusCode(iris.StatusOK)
			ctx.Header("Poll-Interval", "5000")
			ctx.ContentType("application/json")
			ctx.JSON(ret)
			return
		}
	}
	// 获取项目数据
	project := dreleaseproject.Response{}
	err = project.Find(mergeRequest.ReleaseProjectID)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		logging.ErrorLogger.Errorf("Error getting Find:", err)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 验证 工作包状态==>分支状态==>项目状态   0开启 1关闭 2管控
	if project.ID > 0 {
		// SP分支默认放开 TargetBranch 以"_sp" 包含大小写 结尾的分支
		if !strings.Contains(strings.ToLower(mergeRequest.TargetBranch), "_sp") {
			// 项目存在验证工作包状态
			workpackage, err := dmergerequestworkpackage.FindIDAndReleaseProjectID(mergeRequest.WorkPackageID, mergeRequest.ReleaseProjectID)
			if err != nil {
				logging.ErrorLogger.Errorf("Error getting FindIDAndReleaseProjectID:", err)
				ctx.StatusCode(iris.StatusInternalServerError)
				ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
				return
			}
			if workpackage.ID > 0 {
				if workpackage.MrControlStatus == 1 {
					ret["merge_error"] = "该工作包处于关闭阶段，关闭所有合并,如需开启请联系PM"
				}
				if workpackage.MrControlStatus == 2 {
					// 管控状态下审核单是否通过
					// 查找审核单是否存在
					controlReviews, err := dmergerequescontrolreview.FindByMergeRequestID(mergeRequest.ID, mergeRequest.ReleaseProjectID, mergeRequest.WorkPackageID)
					if err != nil {
						logging.ErrorLogger.Errorf("Error getting FindByMergeRequestID:", err)
						ctx.StatusCode(iris.StatusInternalServerError)
						ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
						return
					}
					if controlReviews.ID > 0 {
						if controlReviews.Status == 0 {
							ret["merge_error"] = "该项目处于管控阶段，审批单已生成，等待PM审核中，紧急情况请联系PM进行处理"
						}
					} else {
						ret["merge_error"] = "该项目处于管控阶段，点击合并按钮会自动生成审批单，等待仓库管理员点击合入"
					}
				}
			} else {
				// 工作包不存在，使用分支状态
				branch, err := dbranch.GetBranchByReleaseProjectID(mergeRequest.ReleaseProjectID, mergeRequest.TargetBranch)
				if err != nil {
					logging.ErrorLogger.Errorf("Error getting GetBranchByReleaseProjectID:", err)
					ctx.StatusCode(iris.StatusInternalServerError)
					ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
					return
				}
				if branch.ID > 0 {
					if branch.MrControlStatus == 1 {
						ret["merge_error"] = "该分支处于关闭阶段，关闭所有合并,如需开启请联系PM"
					}
					if branch.MrControlStatus == 2 {
						// 管控状态下审核单是否通过
						// 查找审核单是否存在
						controlReviews, err := dmergerequescontrolreview.FindByMergeRequestID(mergeRequest.ID, mergeRequest.ReleaseProjectID, mergeRequest.WorkPackageID)
						if err != nil {
							logging.ErrorLogger.Errorf("Error getting FindByMergeRequestID:", err)
							ctx.StatusCode(iris.StatusInternalServerError)
							ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
							return
						}
						if controlReviews.ID > 0 {
							if controlReviews.Status == 0 {
								ret["merge_error"] = "该项目处于管控阶段，审批单已生成，等待PM审核中，紧急情况请联系PM进行处理"
							}
						} else {
							ret["merge_error"] = "该项目处于管控阶段，点击合并按钮会自动生成审批单，等待仓库管理员点击合入"
						}
					}
				} else {
					// 工作包不存在，使用项目状态
					if project.MrControlStatus == 1 {
						ret["merge_error"] = "该项目处于关闭阶段，关闭所有合并,如需开启请联系PM"
					}
					if project.MrControlStatus == 2 {
						// 管控状态下审核单是否通过
						// 查找审核单是否存在
						controlReviews, err := dmergerequescontrolreview.FindByMergeRequestID(mergeRequest.ID, mergeRequest.ReleaseProjectID, mergeRequest.WorkPackageID)
						if err != nil {
							logging.ErrorLogger.Errorf("Error getting FindByMergeRequestID:", err)
							ctx.StatusCode(iris.StatusInternalServerError)
							ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
							return
						}
						if controlReviews.ID > 0 {
							if controlReviews.Status == 0 {
								ret["merge_error"] = "该项目处于管控阶段，审批单已生成，等待PM审核中，紧急情况请联系PM进行处理"
							}
						} else {
							ret["merge_error"] = "该项目处于管控阶段，点击合并按钮会自动生成审批单，等待仓库管理员点击合入"
						}
					}
				}
			}
		}
	}
	// 判断是否存在依赖项目
	if ret["merge_error"] == "" || ret["merge_error"] == nil {
		if len(mergeRequest.Dependencies) > 0 {
			var mergeErrorStr string
			for _, dependency := range mergeRequest.Dependencies {
				if dependency.Status != 3 {
					mergeErrorStr = mergeErrorStr + dependency.Title + ","
				}
			}
			if mergeErrorStr != "" {
				ret["merge_error"] = "本次合并需要等待依赖项:" + mergeErrorStr + "MR合入后，一同合并代码"
			}
		}
	}
	ctx.ContentType("application/json")
	ctx.Header("Poll-Interval", "5000")
	ctx.JSON(ret)
}

func WidgetHandler(ctx iris.Context) {
	headers := ctx.Request().Header
	referer := headers.Get("Referer") // 来源链接
	url, ret, iid, err := GitLabProxyRequest(ctx, 2)
	if err != nil {
		logging.ErrorLogger.Errorf("GitLabProxyRequest失败: %s, URL: %s", err.Error(), url)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 获取 IID 和项目路径
	pathWithNamespace, _, err := parseReferer(referer)
	if err != nil {
		logging.ErrorLogger.Errorf("解析Referer失败: %s, Referer: %s", err.Error(), referer)
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}

	privateToken := libs.Config.Buildfarm.Token
	// 通过 token 获取 gitlab client
	gl, err := gitlab.NewClient(privateToken, gitlab.WithBaseURL(libs.Config.Gitlab.Url))
	if err != nil {
		logging.ErrorLogger.Errorf("创建GitLab客户端失败: %s", err.Error())
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 使用Gitlab项目路径和命名空间获取项目
	gitlabProject, _, err := gl.Projects.GetProject(pathWithNamespace, &gitlab.GetProjectOptions{})
	if err != nil {
		logging.ErrorLogger.Errorf("获取GitLab项目失败: %s, 项目路径: %s", err.Error(), pathWithNamespace)
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}

	// 获取 gitlab-merge request详情
	gitlabMergeRequest, _, err := gl.MergeRequests.GetMergeRequest(gitlabProject.ID, iid, &gitlab.GetMergeRequestsOptions{})
	if err != nil {
		logging.ErrorLogger.Errorf("获取GitLab合并请求失败: %s, 项目ID: %d, IID: %d", err.Error(), gitlabProject.ID, iid)
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}
	// 获取merge request数据
	mergeRequest := dmergerequest.MergeRequest{}
	if err := easygorm.GetEasyGormDb().Model(dmergerequest.Model()).
		Where("merge_request_id = ? and target_project_id= ?", gitlabMergeRequest.ID, gitlabProject.ID).
		Preload("User").
		Preload("Assignees").
		Preload("Reviewers").
		Preload("Dependencies").
		Find(&mergeRequest).Error; err != nil {
		logging.ErrorLogger.Errorf("查询MergeRequest数据失败: %s, MergeRequestID: %d, TargetProjectID: %d",
			err.Error(), gitlabMergeRequest.ID, gitlabProject.ID)
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}

	// 不存在的mergerequest原样输出
	if mergeRequest.ID == 0 {
		ctx.StatusCode(iris.StatusOK)
		ctx.Header("Poll-Interval", "5000")
		ctx.ContentType("application/json")
		ctx.JSON(ret)
		return
	}

	// 判断mergerequest状态为-1、3、4或者gitlabMergeRequest状态为merged/closed时原样输出
	if mergeRequest.ID > 0 {
		// 检查本地状态
		if mergeRequest.Status == -1 || mergeRequest.Status == 3 || mergeRequest.Status == 4 {
			ctx.StatusCode(iris.StatusOK)
			ctx.Header("Poll-Interval", "5000")
			ctx.ContentType("application/json")
			ctx.JSON(ret)
			return
		}

		// 检查GitLab状态
		if gitlabMergeRequest.State == "merged" || gitlabMergeRequest.State == "closed" {
			ctx.StatusCode(iris.StatusOK)
			ctx.Header("Poll-Interval", "5000")
			ctx.ContentType("application/json")
			ctx.JSON(ret)
			return
		}
	}
	// 获取项目数据
	project := dreleaseproject.Response{}
	err = project.Find(mergeRequest.ReleaseProjectID)
	if err != nil {
		logging.ErrorLogger.Errorf("获取项目数据失败: %s, ReleaseProjectID: %d",
			err.Error(), mergeRequest.ReleaseProjectID)
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return
	}

	// 验证工作包状态==>分支状态==>项目状态   0开启 1关闭 2管控
	mergeable := true
	if project.ID > 0 {
		// SP分支默认放开，TargetBranch以"_sp"结尾的分支不做管控
		isSPBranch := strings.Contains(strings.ToLower(mergeRequest.TargetBranch), "_sp")

		if !isSPBranch {
			// 非SP分支需要检查工作包、分支和项目状态

			// 1. 先检查工作包状态
			workpackage, err := dmergerequestworkpackage.FindIDAndReleaseProjectID(mergeRequest.WorkPackageID, mergeRequest.ReleaseProjectID)
			if err != nil {
				logging.ErrorLogger.Errorf("查询工作包失败: %s, WorkPackageID: %d, ReleaseProjectID: %d",
					err.Error(), mergeRequest.WorkPackageID, mergeRequest.ReleaseProjectID)
				ctx.StatusCode(iris.StatusInternalServerError)
				ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
				return
			}

			if workpackage.ID > 0 {
				// 工作包存在，按工作包状态判断
				if workpackage.MrControlStatus == 1 {
					// 工作包状态为关闭，不允许合并
					mergeable = false
				} else if workpackage.MrControlStatus == 2 {
					// 工作包状态为管控，检查审核单
					// 查找审核单是否存在
					controlReviews, err := dmergerequescontrolreview.FindByMergeRequestID(mergeRequest.ID, mergeRequest.ReleaseProjectID, mergeRequest.WorkPackageID)
					if err != nil {
						logging.ErrorLogger.Errorf("查找审核单失败: %s, MergeRequestID: %d",
							err.Error(), mergeRequest.ID)
						ctx.StatusCode(iris.StatusInternalServerError)
						ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
						return
					}

					if controlReviews.ID > 0 {
						// 存在审核单，不允许合并
						mergeable = false
					}
				}
			} else {
				// 2. 工作包不存在，检查分支状态
				branch, err := dbranch.GetBranchByReleaseProjectID(mergeRequest.ReleaseProjectID, mergeRequest.TargetBranch)
				if err != nil {
					logging.ErrorLogger.Errorf("查询分支失败: %s, ReleaseProjectID: %d, TargetBranch: %s",
						err.Error(), mergeRequest.ReleaseProjectID, mergeRequest.TargetBranch)
					ctx.StatusCode(iris.StatusInternalServerError)
					ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
					return
				}

				if branch.ID > 0 {
					// 分支存在，按分支状态判断
					if branch.MrControlStatus == 1 {
						// 分支状态为关闭，不允许合并
						mergeable = false
					} else if branch.MrControlStatus == 2 {
						// 分支状态为管控，检查审核单
						// 查找审核单是否存在
						controlReviews, err := dmergerequescontrolreview.FindByMergeRequestID(mergeRequest.ID, mergeRequest.ReleaseProjectID, mergeRequest.WorkPackageID)
						if err != nil {
							logging.ErrorLogger.Errorf("查找审核单失败: %s, MergeRequestID: %d",
								err.Error(), mergeRequest.ID)
							ctx.StatusCode(iris.StatusInternalServerError)
							ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
							return
						}

						if controlReviews.ID > 0 {
							// 存在审核单，不允许合并
							mergeable = false
						}
					}
				} else {
					// 3. 分支不存在，使用项目状态
					if project.MrControlStatus == 1 {
						// 项目状态为关闭，不允许合并
						mergeable = false
					} else if project.MrControlStatus == 2 {
						// 项目状态为管控，检查审核单
						// 查找审核单是否存在
						controlReviews, err := dmergerequescontrolreview.FindByMergeRequestID(mergeRequest.ID, mergeRequest.ReleaseProjectID, mergeRequest.WorkPackageID)
						if err != nil {
							logging.ErrorLogger.Errorf("查找审核单失败: %s, MergeRequestID: %d",
								err.Error(), mergeRequest.ID)
							ctx.StatusCode(iris.StatusInternalServerError)
							ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
							return
						}

						if controlReviews.ID > 0 {
							// 存在审核单，不允许合并
							mergeable = false
						}
					}
				}
			}
		}
	}
	// 判断按钮点击状态
	if mergeable {
		if mergeRequest.ButtonClickStatus == 1 {
			// 如果ButtonClickStatus为1，检查MR状态
			if gitlabMergeRequest.State == "opened" {
				// 当MR状态为opened时，重置ButtonClickStatus为0
				mergeRequestUp := dmergerequest.MergeRequest{}
				updateErr := mergeRequestUp.Update(mergeRequest.ID, map[string]interface{}{"ButtonClickStatus": 0})
				if updateErr != nil {
					logging.ErrorLogger.Errorf("ButtonClickStatus更新失败: %s", updateErr.Error())
				} else {
					mergeable = true
				}
			} else {
				// 非opened状态，保持按钮不可用
				mergeable = false
			}
		} else {
			mergeable = true
		}
	}
	// 修改 JSON 数据---按钮状态
	ret["mergeable"] = mergeable
	// 设置自定义响应头
	ctx.Header("Poll-Interval", "5000")
	ctx.ContentType("application/json")
	ctx.JSON(ret)
}

func copyHeader(dst, src http.Header) {
	for k, vv := range src {
		for _, v := range vv {
			dst.Add(k, v)
		}
	}
}

func GitLabProxyRequest(ctx iris.Context, reqtype int) (string, map[string]interface{}, int, error) {
	var ret map[string]interface{}
	var iid int
	headers := ctx.Request().Header
	referer := headers.Get("Referer")
	url := libs.Config.Gitlab.Url + "/" // libs.Config.Gitlab.Url
	pathWithNamespace, iidStr, err := parseReferer(referer)
	// gitlab-instance-aa5fe906/Monitoring
	if err != nil {
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.JSON(iris.Map{"status": "failed", "error": err.Error()})
		return url, ret, iid, err
	}
	// 将字符串 IID 转换为整型
	iid, err = strconv.Atoi(iidStr)
	if err != nil {
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.JSON(iris.Map{"status": "failed", "error": "Invalid IID"})
		return url, ret, iid, err
	}

	client := &http.Client{}
	var req *http.Request
	var _url string
	switch reqtype {
	case 1:
		_url = url + pathWithNamespace + "/-/merge_requests/" + iidStr + "/cached_widget.json?real=1"
		req, err = http.NewRequest("GET", _url, nil)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			ctx.WriteString(err.Error())
			return _url, ret, iid, err
		}
	case 2:
		_url = url + pathWithNamespace + "/-/merge_requests/" + iidStr + "/widget.json?real=1"
		req, err = http.NewRequest("GET", _url, nil)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			ctx.WriteString(err.Error())
			return _url, ret, iid, err
		}
	}
	copyHeader(req.Header, headers)
	resp, err := client.Do(req)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.WriteString(err.Error())
		return _url, ret, iid, err
	}
	defer resp.Body.Close()

	var reader io.ReadCloser
	switch resp.Header.Get("Content-Encoding") {
	case "gzip":
		reader, err = gzip.NewReader(resp.Body)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			ctx.WriteString(err.Error())
			return _url, ret, iid, err
		}
		defer reader.Close()
	default:
		reader = resp.Body
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.WriteString(err.Error())
		return _url, ret, iid, err
	}

	if err := json.Unmarshal(body, &ret); err != nil {
		logging.ErrorLogger.Errorf(string(body))
		ctx.StatusCode(iris.StatusInternalServerError)
		ctx.WriteString(err.Error())
		return _url, ret, iid, err
	}
	return _url, ret, iid, nil
}

func GetMRReviews(ctx iris.Context) {
	page, _ := strconv.Atoi(ctx.FormValue("page"))
	pageSize, _ := strconv.Atoi(ctx.FormValue("pageSize"))
	orderBy := ctx.FormValue("orderBy")
	sort := ctx.FormValue("sort")
	projectID, _ := strconv.Atoi(ctx.FormValue("project"))
	status, _ := strconv.Atoi(ctx.FormValue("status"))
	ret, err := dmergerequescontrolreview.FindByReleaseProjectID(projectID, page, pageSize, status, sort, orderBy)
	if err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, response.SystemErr.Msg))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, ret, response.NoErr.Msg))
	return
}

func GetMRReview(ctx iris.Context) {
	info := dmergerequescontrolreview.Response{}
	err := dao.Find(&info, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get user get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	ctx.JSON(response.NewResponse(response.NoErr.Code, info, response.NoErr.Msg))
}

func HandleMrControlReview(ctx iris.Context) {
	uId, err := dao.GetAuthId(ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("dao all get auth id get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "登陆状态异常，请重新登陆"))
		return
	}

	review := dmergerequescontrolreview.Response{}
	err = dao.Find(&review, ctx)
	if err != nil {
		logging.ErrorLogger.Errorf("get project get err ", err)
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
		return
	}
	fmt.Println(uId)
	project := dreleaseproject.Response{}
	if err := project.Find(review.ReleaseProject.ID); err != nil {
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "该项目不存在！"))
		return
	}
	if !libs.InArrayUint([]uint{project.PmID, 1}, uId) {
		ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "无权限进行操作"))
		return
	}
	reviewID, _ := dao.GetId(ctx)

	handle := ctx.FormValue("type")
	comment := ctx.FormValue("comment")
	switch handle {
	case "pass":
		// 获取mergerequest数据
		mergeRequest := review.MergeRequest
		// 判断是否存在依赖项目
		dependencyStatus := false
		var dependencyTitle string
		if len(mergeRequest.Dependencies) > 0 {
			for _, dependency := range mergeRequest.Dependencies {
				if dependency.Status != 3 {
					dependencyTitle = dependency.Title
					dependencyStatus = true
					break
				}
			}
		}
		if dependencyStatus {
			ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "存在依赖项目未合并请先合并依赖项《"+dependencyTitle+"》"))
			return
		}
		// 通过用户名获取gitlab私有token
		url := libs.Config.Gitlab.Url
		if mergeRequest.ButtonClickUser == "" {
			ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "参数错误,该MR表单已重置，请拒绝！"))
			return
		}
		if mergeRequest.Status == -1 || mergeRequest.Status == 3 || mergeRequest.Status == 4 {
			ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "参数错误,该MR请求已合并或已关闭，请拒绝！"))
			return
		}
		privateToken, _, err := GetGitlabTokenByUserName(mergeRequest.ButtonClickUser)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		// 通过 token 获取 gitlab client
		gl, err := gitlab.NewClient(privateToken, gitlab.WithBaseURL(url))
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		// 获取 gitlab-merge request详情
		gitlabMergeRequest, _, err := gl.MergeRequests.GetMergeRequest(int(mergeRequest.TargetProjectID), int(mergeRequest.MergeRequestIID), &gitlab.GetMergeRequestsOptions{})
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		// 对比SHA值
		if gitlabMergeRequest.SHA != mergeRequest.MergeRequestSHA {
			// sha值不对重置状态
			mergerequestObjects := map[string]interface{}{
				"ButtonClickStatus": 0,
				"ButtonClickUser":   "",
				"MergeRequestSHA":   "",
			}
			mergerequestUP := dmergerequest.MergeRequest{}
			err = mergerequestUP.Update(mergeRequest.ID, mergerequestObjects)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			// 审批单关闭
			controlreviewObjects := map[string]interface{}{
				"Status":  3,
				"Comment": "参数异常：MR表单SHA不一致，系统自动关闭",
			}
			controlreviewUP := dmergerequescontrolreview.Response{}
			err = controlreviewUP.Update(reviewID, controlreviewObjects)
			if err != nil {
				ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
				return
			}
			// 发送邮件通知重新审核合并-->合并者
			go SendMailToReviewUser(*mergeRequest, 2)
			ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "MR表单SHA不一致，需重新发起流程"))
			return
		}
		// MR合并请求
		sha := mergeRequest.MergeRequestSHA
		_, resp, err := gl.MergeRequests.AcceptMergeRequest(int(mergeRequest.TargetProjectID), int(mergeRequest.MergeRequestIID), &gitlab.AcceptMergeRequestOptions{SHA: &sha})
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			ctx.JSON(iris.Map{"status": "failed", "error5": err.Error()})
			return
		}
		fmt.Println(resp)
		// 验证MR合并是否成功
		gitlabMergeRequestNew, _, err := gl.MergeRequests.GetMergeRequest(int(mergeRequest.TargetProjectID), int(mergeRequest.MergeRequestIID), &gitlab.GetMergeRequestsOptions{})
		if err != nil {
			logging.ErrorLogger.Errorf("重新获取MR信息失败:", err.Error())
		}
		// 如果状态还是opened，则重置ButtonClickStatus
		if gitlabMergeRequestNew.State == "opened" {
			mergeRequestUp := dmergerequest.MergeRequest{}
			UpErr := mergeRequestUp.Update(mergeRequest.ID, map[string]interface{}{"ButtonClickStatus": 0})
			if UpErr != nil {
				logging.ErrorLogger.Errorf("ButtonClickStatus更新失败:", UpErr.Error())
			}
			ctx.JSON(response.NewResponse(response.PermitErr.Code, nil, "MR合并失败，请重新再试！"))
			return
		}

		// 更新管控MR审核表单状态
		controlreviewObjects := map[string]interface{}{
			"Status":  1,
			"Comment": comment,
		}
		controlreviewUP := dmergerequescontrolreview.Response{}
		err = controlreviewUP.Update(review.ID, controlreviewObjects)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		// 发送邮件通知该项MR已经合并完成--合并者、提交人
		go SendMailSuccess(*mergeRequest)
		// 被依赖项合并
		go MergeDependentMRs(*mergeRequest)
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return

	case "reject":
		// 获取mergerequest数据
		mergeRequest := review.MergeRequest
		mergerequestObjects := map[string]interface{}{
			"ButtonClickStatus": 0,
			"ButtonClickUser":   "",
			"MergeRequestSHA":   "",
		}
		mergerequestUP := dmergerequest.MergeRequest{}
		err = mergerequestUP.Update(mergeRequest.ID, mergerequestObjects)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		// 更新管控MR审核表单状态
		controlreviewObjects := map[string]interface{}{
			"Status":  2,
			"Comment": comment,
		}
		controlreviewUP := dmergerequescontrolreview.Response{}
		err = controlreviewUP.Update(reviewID, controlreviewObjects)
		if err != nil {
			ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, err.Error()))
			return
		}
		// 发送邮件通知重新审核合并-->合并者
		go SendMailToReviewUser(*mergeRequest, 1)
		ctx.JSON(response.NewResponse(response.NoErr.Code, nil, response.NoErr.Msg))
		return

	default:
		ctx.JSON(response.NewResponse(response.SystemErr.Code, nil, "未提供操作"))
		return
	}
}

func parseReferer(referer string) (string, string, error) {
	var iidStr string
	var pathWithNamespace string
	// 使用正则表达式提取项目路径和 merge request IID
	pattern := regexp.MustCompile(`https?://[^/]+/((?:[^/]+/)+[^/]+)/-/merge_requests/(\d+)`)
	matches := pattern.FindStringSubmatch(referer)
	if len(matches) < 3 {
		logging.ErrorLogger.Errorf("invalid merge request url:" + referer)
		return pathWithNamespace, iidStr, errors.New("invalid merge request url:" + referer)
	}
	pathWithNamespace = matches[1] // 项目路径和命名空间
	iidStr = matches[2]            // merge request IID 字符串
	return pathWithNamespace, iidStr, nil
}

// 通过用户名获取用户gitlabToken数据
func GetGitlabTokenByUserName(username string) (string, uint, error) {
	userRes := &duser.User{}
	var privateToken string
	err := userRes.FindByUserName(username)
	if err != nil {
		logging.ErrorLogger.Errorf("get user by username err ", err)
		return privateToken, 0, err
	}
	// 查找用户的gitlabToken
	userGitLabToken := dgitlabtoken.Response{}
	err = userGitLabToken.FindEx("user_id", strconv.FormatUint(uint64(userRes.ID), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		return privateToken, 0, err
	}
	if libs.Config.Gitlab.IsInternal {
		privateToken = userGitLabToken.Token
	} else {
		privateToken = userGitLabToken.OutToken
	}
	if privateToken == "" {
		// 创建新的GitLabToken
		privateToken, err = CreatGitlabTokenByUserName(username)
		if err != nil {
			return privateToken, 0, err
		}
	}
	return privateToken, userRes.ID, nil
}

// 创建新的GitLabToken
func CreatGitlabTokenByUserName(username string) (string, error) {
	userRes := &duser.User{}
	var privateToken string
	err := userRes.FindByUserName(username)
	if err != nil {
		logging.ErrorLogger.Errorf("get user by username err ", err)
		return privateToken, err
	}
	// 创建 GitLab 客户端
	client, err := gitlab.NewClient(libs.Config.Gitlab.Token, gitlab.WithBaseURL(libs.Config.Gitlab.Url+"/api/v4"))
	if err != nil {
		logging.ErrorLogger.Errorf("Failed to create GitLab client: ", err)
		return privateToken, errors.New("Failed to create GitLab client:" + err.Error())
	}
	// 查询指定用户名的用户
	users, _, err := client.Users.ListUsers(&gitlab.ListUsersOptions{Username: &username})
	if err != nil {
		logging.ErrorLogger.Errorf("Failed to fetch user information for %s: %v", username, err)
		return privateToken, errors.New("Failed to fetch user information for:" + username + " " + err.Error())
	}
	if len(users) == 0 {
		logging.ErrorLogger.Errorf("User with username %s not found", username)
		return privateToken, errors.New("User with username  " + username + "not found")
	}
	var foundUser *gitlab.User
	// 检查每个用户，找到完全匹配的那一个
	for _, gitlabUser := range users {
		if gitlabUser.Username == username {
			foundUser = gitlabUser
			break
		}
	}
	if foundUser == nil {
		logging.ErrorLogger.Errorf("No user found with the username: %s", username)
		return privateToken, errors.New("No user found with the username: " + username)
	}
	// 创建 Personal Access Token 请求
	loc, _ := time.LoadLocation("Asia/Shanghai")
	expiresAt := gitlab.ISOTime(time.Date(2099, 12, 31, 23, 59, 59, 0, loc))
	createTokenRequest := &gitlab.CreatePersonalAccessTokenOptions{
		Name:      gitlab.Ptr("MyAccessToken"),
		Scopes:    &[]string{"api"},
		ExpiresAt: &expiresAt,
	}
	token, _, err := client.Users.CreatePersonalAccessToken(foundUser.ID, createTokenRequest)
	if err != nil {
		logging.ErrorLogger.Errorf("Failed to CreatePersonalAccessToken: ", err)
		return privateToken, errors.New("Failed to CreatePersonalAccessToken: " + err.Error())
	}
	privateToken = token.Token
	// 查找用户的gitlabToken
	userGitLabToken := dgitlabtoken.Response{}
	err = userGitLabToken.FindEx("user_id", strconv.FormatUint(uint64(userRes.ID), 10))
	if err != nil {
		logging.ErrorLogger.Errorf("get token for get git projects err ", err)
		return privateToken, errors.New("get token for get git projects err " + err.Error())
	}
	// 保存到GitlabToken 表中
	if userGitLabToken.Id == 0 {
		DGitlabToken := dgitlabtoken.Response{}
		gitlabObjects := map[string]interface{}{
			"UserId":    userRes.ID,
			"CreatedAt": time.Now(),
			"UpdatedAt": time.Now(),
		}
		if libs.Config.Gitlab.IsInternal {
			gitlabObjects["Token"] = privateToken
		} else {
			gitlabObjects["OutToken"] = privateToken
		}
		err = DGitlabToken.Create(gitlabObjects)
		if err != nil {
			logging.ErrorLogger.Errorf("create user token get err ", err)
			return privateToken, errors.New("create user token get err " + err.Error())
		}
	} else {
		gitlabObjects := map[string]interface{}{
			"UserId":    userRes.ID,
			"UpdatedAt": time.Now(),
		}
		if libs.Config.Gitlab.IsInternal {
			gitlabObjects["Token"] = privateToken
		} else {
			gitlabObjects["OutToken"] = privateToken
		}
		err = userGitLabToken.Update(userGitLabToken.Id, gitlabObjects)
		if err != nil {
			logging.ErrorLogger.Errorf("create user token get err ", err)
			return privateToken, errors.New("create user token get err " + err.Error())
		}
	}
	return privateToken, nil
}

func getGitlabUserInfoByCookie(URL string, ctx iris.Context) (string, error) {
	gitlabCookie := ctx.GetCookie("_gitlab_session")
	if gitlabCookie == "" {
		ctx.StatusCode(iris.StatusBadRequest)
		ctx.JSON(iris.Map{"error": "No GitLab access token found in cookies"})
		return "", errors.New("no GitLab access token found in cookies")
	}
	// 创建一个HTTP客户端
	client := &http.Client{}

	// 创建请求
	req, err := http.NewRequest("GET", URL+"api/v4/user", nil)
	if err != nil {
		return "", err
	}
	// 配置cookie
	req.AddCookie(&http.Cookie{Name: "_gitlab_session", Value: gitlabCookie})

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 检查是否成功获取到了数据
	if resp.StatusCode != http.StatusOK {
		return "", err
	}
	// 解析 JSON 数据
	var ret GitLabUser
	if err := json.Unmarshal(body, &ret); err != nil {
		return "", err
	}
	return ret.Username, nil
}

// 状态编码函数，将三个状态编码为一个三位数的编号 项目、分支、工作包
func EncodeStatus(projectStatus, branchStatus, packageStatus int) (int, string) {
	encodedStatus := fmt.Sprintf("%03d", projectStatus*100+branchStatus*10+packageStatus)
	switch encodedStatus {
	case "000":
		return 0, "打开"
	case "001":
		return 3, "存在部分工作包为关闭状态"
	case "002":
		return 4, "存在部分工作包为管控状态"
	case "010":
		return 5, "存在部分分支为关闭状态"
	case "011":
		return 6, "存在部分分支和工作包均为关闭状态"
	case "012":
		return 7, "存在部分分支为关闭状态,部分工作包为管控状态"
	case "020":
		return 8, "存在部分分支为管控状态"
	case "021":
		return 9, "存在部分分支为管控状态，部分工作包为关闭状态"
	case "022":
		return 10, "存在部分分支和工作包均为管控状态"
	case "100":
		return 11, "存在部分分支和工作包均为打开状态"
	case "101":
		return 12, "存在部分分支为打开状态"
	case "102":
		return 13, "存在部分分支为打开状态，部分工作包为管控状态"
	case "110":
		return 14, "存在部分工作包为打开状态"
	case "111":
		return 1, "关闭"
	case "112":
		return 15, "存在部分工作包为管控状态"
	case "120":
		return 16, "存在部分分支为管控状态，部分工作包为打开状态"
	case "121":
		return 17, "存在部分分支为管控状态"
	case "122":
		return 18, "存在部分分支和工作包均为管控状态 "
	case "200":
		return 19, "存在部分分支和工作包均为打开状态 "
	case "201":
		return 20, "存在部分分支为打开状态，部分工作包为关闭状态"
	case "202":
		return 21, "存在部分分支为打开状态"
	case "210":
		return 22, "存在部分分支为关闭状态，部分工作包为打开状态"
	case "211":
		return 23, "存在部分分支和工作包均为关闭状态"
	case "212":
		return 24, "存在部分分支为关闭状态"
	case "220":
		return 25, "存在部分工作包为打开状态"
	case "221":
		return 26, "存在部分工作包为关闭状态"
	case "222":
		return 2, "管控"
	// 添加其他状态组合的编码
	default:
		return 27, "状态异常"
	}
}

var httpClient = &http.Client{
	Transport: &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false, // 设置为 true 可跳过证书验证
		},
	},
}

func ProxyHandler(ctx iris.Context) {
	// 获取并验证请求中的 URL 参数
	originalURL := ctx.URLParam("url")
	if originalURL == "" {
		handleError(ctx, http.StatusBadRequest, "缺失 'url' 查询参数")
		return
	}
	parsedURL, err := url.Parse(originalURL)
	if err != nil {
		handleError(ctx, http.StatusBadRequest, "Invalid 'url' query parameter")
		return
	}
	// 构造目标 URL
	targetHost := libs.Config.Gitlab.Url
	// 获取查询参数并添加 real=1
	queryParams := parsedURL.Query()
	queryParams.Add("real", "1")
	// 更新 URL 的查询参数
	parsedURL.RawQuery = queryParams.Encode()
	// 构造目标 URL
	targetURL := targetHost + parsedURL.String()
	// 特殊逻辑处理：POST 请求且状态事件为 close
	if ctx.Method() == http.MethodPost && queryParams.Get("merge_request[state_event]") == "close" {
		if err := handlePostCloseLogic(targetURL); err != nil {
			handleError(ctx, http.StatusInternalServerError, err.Error())
			return
		}
	}
	// 转发请求
	if err := forwardRequest(ctx, targetURL); err != nil {
		handleError(ctx, http.StatusBadGateway, "Failed to forward request to target server")
	}
}

// handleError 错误响应逻辑
func handleError(ctx iris.Context, status int, message string) {
	ctx.StatusCode(status)
	ctx.WriteString(message)
	logging.ErrorLogger.Errorf("%s: %d", message, status)
}

// handlePostCloseLogic 处理特殊逻辑
func handlePostCloseLogic(targetURL string) error {
	client, err := gitlab.NewClient(
		libs.Config.Buildfarm.Token,
		gitlab.WithBaseURL(libs.Config.Gitlab.Url),
	)
	if err != nil {
		return fmt.Errorf("创建 GitLab 客户端失败: %w", err)
	}
	pathWithNamespace, iidStr, err := parseReferer(targetURL)
	if err != nil {
		return fmt.Errorf("解析 Referer 失败: %w", err)
	}
	iid, err := strconv.Atoi(iidStr)
	if err != nil {
		return fmt.Errorf("无效的 IID: %w", err)
	}
	// 获取 GitLab 项目与 MR 信息
	gitlabProject, _, err := client.Projects.GetProject(pathWithNamespace, nil)
	if err != nil {
		return fmt.Errorf("获取项目失败: %w", err)
	}
	gitlabMR, _, err := client.MergeRequests.GetMergeRequest(gitlabProject.ID, iid, nil)
	if err != nil {
		return fmt.Errorf("获取 MR 详情失败: %w", err)
	}
	mergeRequest, err := dmergerequest.GetMergeRequestByMRIDAndTargetProjectID(gitlabMR.ID, gitlabProject.ID)
	if err != nil {
		return fmt.Errorf("查询 MR 数据失败: %w", err)
	}
	// 校验与操作
	if err := validateAndProcessMR(client, &mergeRequest); err != nil {
		return fmt.Errorf("处理 MR 时出错: %w", err)
	}
	return nil
}

// validateAndProcessMR 校验并处理 Merge Request
func validateAndProcessMR(client *gitlab.Client, mergeRequest *dmergerequest.MergeRequest) error {
	// 验证关联 BUG 状态
	if len(mergeRequest.BugID) > 0 {
		if err := validateBugState(mergeRequest.BugID); err != nil {
			return err
		}
	}
	// 关闭依赖的 Merge Request
	if err := closeDependentMRs(client, mergeRequest); err != nil {
		return err
	}
	return nil
}

// validateBugState 验证 BUG 状态
func validateBugState(bugIDs string) error {
	resp, err := BugWebClient.R().Get("http://10.51.134.126:9008/api/v1/datasync/bug")
	if err != nil || resp.IsErrorState() {
		return fmt.Errorf("同步 BUG 数据失败")
	}
	bugs, err := dbug.FindBugInBugIDs(strings.Split(bugIDs, ","))
	if err != nil {
		return fmt.Errorf("查询 BUG 数据失败: %w", err)
	}
	for _, bug := range bugs {
		if bug.BugState == "DELAY" {
			return fmt.Errorf("BUG_ID:%d状态为DELAY，不允许关闭MR，需要推进至RESOLVED/CHECKED，", bug.BugID)
		}
	}
	return nil
}

// closeDependentMRs 关闭依赖的 Merge Request
func closeDependentMRs(client *gitlab.Client, mergeRequest *dmergerequest.MergeRequest) error {
	var mrItems []*dmergerequesdependencies.ListResponse
	if err := easygorm.GetEasyGormDb().
		Model(dmergerequesdependencies.Model()).
		Where("dependency_id = ?", mergeRequest.ID).
		Find(&mrItems).Error; err != nil {
		return fmt.Errorf("查询依赖 MR 时出错: %w", err)
	}

	if len(mrItems) > 0 {
		mrIDs := make([]uint, 0, len(mrItems))
		for _, item := range mrItems {
			mrIDs = append(mrIDs, item.MergeRequestID)
		}

		dependentMRs, err := dmergerequest.FindInIds(mrIDs)
		if err != nil {
			return fmt.Errorf("查询依赖 MR 数据失败: %w", err)
		}

		for _, mr := range dependentMRs {
			if err := CloseMergeRequest(client, int(mr.TargetProjectID), int(mr.MergeRequestIID)); err != nil {
				return fmt.Errorf("关闭 MR 时出错: %w", err)
			}
			// 发送邮件通知
			SendCloseMergeRequestMail(mr, mergeRequest)
		}
	}

	return nil
}

// forwardRequest 转发请求
func forwardRequest(ctx iris.Context, targetURL string) error {
	reqBody, err := readRequestBody(ctx)
	if err != nil {
		return err
	}
	proxyReq, err := http.NewRequest(ctx.Method(), targetURL, reqBody)
	if err != nil {
		return fmt.Errorf("创建转发请求失败: %w", err)
	}
	copyHeaders(ctx.Request().Header, proxyReq.Header)
	// 转发请求
	resp, err := httpClient.Do(proxyReq)
	if err != nil {
		return fmt.Errorf("转发请求失败: %w", err)
	}
	defer resp.Body.Close()
	// 转发响应
	copyHeaders(resp.Header, ctx.ResponseWriter().Header())
	ctx.StatusCode(resp.StatusCode)
	_, err = io.Copy(ctx.ResponseWriter(), resp.Body)
	return err
}

// readRequestBody 读取请求体
func readRequestBody(ctx iris.Context) (io.Reader, error) {
	if ctx.Request().Body == nil {
		return nil, nil
	}
	bodyBytes, err := io.ReadAll(ctx.Request().Body)
	if err != nil {
		return nil, fmt.Errorf("读取请求体失败: %w", err)
	}
	return bytes.NewReader(bodyBytes), nil
}

// copyHeaders 复制请求或响应头
func copyHeaders(src, dst http.Header) {
	for key, values := range src {
		for _, value := range values {
			dst.Add(key, value)
		}
	}
}

// CloseMergeRequest 关闭 MR
func CloseMergeRequest(client *gitlab.Client, projectID, MergeRequestIID int) error {
	// 准备请求参数
	options := &gitlab.UpdateMergeRequestOptions{
		StateEvent: gitlab.Ptr("close"),
	}
	// 调用 API 更新 MR 状态
	mergeRequest, _, err := client.MergeRequests.UpdateMergeRequest(projectID, MergeRequestIID, options)
	if err != nil {
		return fmt.Errorf("failed to close merge request: %w", err)
	}
	// 输出成功信息
	fmt.Printf("Merge Request #%d in project '%d' has been closed.\n", mergeRequest.IID, projectID)
	return nil
}

// SendCloseMergeRequestMail 发送MR关闭的邮件通知
func SendCloseMergeRequestMail(mr *dmergerequest.MergeRequest, masterMR *dmergerequest.MergeRequest) error {
	subject := fmt.Sprintf("[MR表单系统][MR标题:%s][因依赖项的MR关闭,本次合并请求已关闭]", mr.Title)
	body := fmt.Sprintf(`%s<br><p>因为所依赖的MR关闭（[MR标题:%s)，本次合并同步关闭</p><br><p>请您在<a href="http://10.51.135.15:9090/mergerequest/#/mergerequest/index">MR表单系统</a>中，进行重新提交<p>`, subject, masterMR.Title)
	mailTo := []string{mr.User.Username + "@ruijie.com.cn"}
	cc := []string{"<EMAIL>"}
	if !libs.Config.Debug {
		libs.SendMailRedis("MR表单系统", mailTo, subject, body, cc)
	}
	logging.DebugLogger.Debugf("send mail", mailTo, subject, body)
	return nil
}

func SendMailToPM(mergeRequest dmergerequest.MergeRequest) {
	subject := fmt.Sprintf("[MR表单系统][MR标题:%s][MR管控审核单等待审批][待处理]", mergeRequest.Title)
	body := fmt.Sprintf(`%s<br><p>请您在<a href="http://aqyfzx.ruijie.net:9090/release/#/branch/mrcontrolreview">MR表单系统</a>中，进行审核操作<p>`, subject)
	cc := []string{"<EMAIL>"}
	if !libs.Config.Debug {
		libs.SendMailRedis("MR表单系统", []string{fmt.Sprintf("%<EMAIL>", mergeRequest.RelateReleaseProject.Pm.Username)}, subject, body, cc)
	}
}

func SendMailToReviewUser(mergeRequest dmergerequest.MergeRequest, sendType int) {
	var subject string
	if sendType == 1 {
		subject = fmt.Sprintf("[MR表单系统][MR标题:%s][MR管控审核单未通过][联系PM进行处理]", mergeRequest.Title)
	} else {
		subject = fmt.Sprintf("[MR表单系统][MR标题:%s][有新代码提交需要重新审核][待处理]", mergeRequest.Title)
	}

	body := fmt.Sprintf(`%s<br><p>请您在<a href="%s">Gitlab仓库</a>或在<a href="http://10.51.135.15:9090/mergerequest/#/mergerequest/index">MR表单系统</a>中，进行重新审核Review<p>`, subject, libs.Config.Gitlab.Url)
	cc := []string{"<EMAIL>"}
	var mailTo []string
	if !libs.InArrayS(mailTo, fmt.Sprintf("%<EMAIL>", mergeRequest.ButtonClickUser)) {
		mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", mergeRequest.ButtonClickUser))
	}
	if !libs.InArrayS(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[mergeRequest.UserID].Username)) {
		mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[mergeRequest.UserID].Username))
	}
	if !libs.Config.Debug {
		libs.SendMailRedis("MR表单系统", mailTo, subject, body, cc)
	}
}

func SendMailSuccess(mergeRequest dmergerequest.MergeRequest) {
	var mailTo []string
	cc := []string{"<EMAIL>"}
	if !libs.InArrayS(mailTo, fmt.Sprintf("%<EMAIL>", mergeRequest.ButtonClickUser)) {
		mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", mergeRequest.ButtonClickUser))
	}
	if !libs.InArrayS(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[mergeRequest.UserID].Username)) {
		mailTo = append(mailTo, fmt.Sprintf("%<EMAIL>", duser.UserMap[mergeRequest.UserID].Username))
	}
	subject := fmt.Sprintf("[MR表单系统][MR标题:%s][合并完成通知]", mergeRequest.Title)
	body := fmt.Sprintf(`%s<br><p>您可以登录在<a href="%s">Gitlab仓库</a>或在<a href="http://10.51.135.15:9090/mergerequest/#/mergerequest/index">MR表单系统</a>中，查看详情。<p>`, subject, libs.Config.Gitlab.Url)
	if !libs.Config.Debug {
		libs.SendMailRedis("MR表单系统", mailTo, subject, body, cc)
	}
}

// 关联被依赖项合并
func MergeDependentMRs(mergeRequest dmergerequest.MergeRequest) error {
	// 查找当前MR的被依赖项目集合
	url := libs.Config.Gitlab.Url + "/"
	var MrItems []*dmergerequesdependencies.ListResponse
	if err := easygorm.GetEasyGormDb().Model(dmergerequesdependencies.Model()).Where("dependency_id = ?", mergeRequest.ID).Find(&MrItems).Error; err != nil {
		return fmt.Errorf("error finding dependent MRs: %w", err)
	}

	if len(MrItems) == 0 {
		return nil
	}
	uniqueIds := make(map[uint]bool)
	MrIds := []uint{}
	for _, mrItem := range MrItems {
		if _, ok := uniqueIds[mrItem.MergeRequestID]; !ok {
			uniqueIds[mrItem.MergeRequestID] = true
			MrIds = append(MrIds, mrItem.MergeRequestID)
		}
	}
	dependencyMR, err := dmergerequest.FindInIds(MrIds)
	if err != nil {
		return fmt.Errorf("error retrieving MRs by IDs: %w", err)
	}
	for _, dItem := range dependencyMR {
		if err := ProcessDependencyMR(url, *dItem, mergeRequest.ID); err != nil {
			logging.ErrorLogger.Error(fmt.Sprintf("Error processing dependency MR: %s", err.Error()))
			continue
		}
	}
	return nil
}

func ProcessDependencyMR(url string, dItem dmergerequest.MergeRequest, currentMRID uint) error {
	dcontrolReviews := &dmergerequescontrolreview.Response{}
	if dItem.Status == -1 || dItem.Status == 3 || dItem.Status == 4 {
		return nil
	}
	if err := easygorm.GetEasyGormDb().Model(dmergerequescontrolreview.Model()).
		Where("merge_request_id = ? AND release_project_id = ? AND work_package_id = ? AND status = 0", dItem.ID, dItem.ReleaseProjectID, dItem.WorkPackageID).Find(dcontrolReviews).Error; err != nil {
		return fmt.Errorf("error fetching control reviews: %w", err)
	}

	if dcontrolReviews.ID > 0 {
		return nil
	}

	if dItem.ButtonClickStatus != 1 {
		return nil
	}

	ddependencyStatus := false
	for _, dependency := range dItem.Dependencies {
		if dependency.Status != 3 && dependency.ID != currentMRID {
			ddependencyStatus = true
			break
		}
	}

	if ddependencyStatus {
		return nil
	}

	// 通过用户名获取gitlab私有token
	privateToken, _, err := GetGitlabTokenByUserName(dItem.ButtonClickUser)
	if err != nil {
		return fmt.Errorf("error getting GitLab token: %w", err)
	}

	glV2, err := gitlab.NewClient(privateToken, gitlab.WithBaseURL(url))
	if err != nil {
		return fmt.Errorf("error creating GitLab client: %w", err)
	}

	// 对比SHA值
	gitlabMergeRequestV2, _, err := glV2.MergeRequests.GetMergeRequest(int(dItem.TargetProjectID), int(dItem.MergeRequestIID), &gitlab.GetMergeRequestsOptions{})
	if err != nil {
		return fmt.Errorf("error getting GitLab merge request: %w", err)
	}

	if gitlabMergeRequestV2.SHA != dItem.MergeRequestSHA {
		// SHA值不对重置状态
		ddependencyObjects := map[string]interface{}{
			"ButtonClickStatus": 0,
			"ButtonClickUser":   "",
			"MergeRequestSHA":   "",
		}
		mergeRequestUp := dmergerequest.MergeRequest{}
		if err := mergeRequestUp.Update(dItem.ID, ddependencyObjects); err != nil {
			return fmt.Errorf("error updating MR: %w", err)
		}

		// 发送邮件通知重新审核合并-->合并者
		go SendMailToReviewUser(dItem, 1)
		return nil
	}

	_, _, err = glV2.MergeRequests.AcceptMergeRequest(int(dItem.TargetProjectID), int(dItem.MergeRequestIID), &gitlab.AcceptMergeRequestOptions{SHA: &dItem.MergeRequestSHA})
	if err != nil {
		return fmt.Errorf("error accepting merge request: %w", err)
	}
	if err := MergeDependentMRs(dItem); err != nil {
		logging.ErrorLogger.Error(fmt.Sprintf("Error in recursive mergeDependentMRs call: %s", err.Error()))
	}
	// 发送邮件通知该项MR已经合并完成--合并者、提交人
	go SendMailSuccess(dItem)
	return nil
}

// 当项目打开时，处理项目中点击过合并按钮，但是未合并的合并请求
func ProcessUnmergedMRsByProjectID(projectID, workPackageID uint, targetBranch string) error {
	var mergeRequestItems []*dmergerequest.MergeRequest
	db := easygorm.GetEasyGormDb().Model(dmergerequest.Model()).Preload("User").Preload("Assignees").Preload("Reviewers").Preload("Dependencies")
	where := easygorm.GetEasyGormDb().Model(dmergerequest.Model()).Where("release_project_id = ? and button_click_status= 1 and status not in(-1,3,4)", projectID)
	if workPackageID > 0 {
		where = where.Where("work_package_id = ?", workPackageID)
	}
	if targetBranch != "" {
		where = where.Where("target_branch = ?", targetBranch)
	}
	if err := db.Where(where).Find(&mergeRequestItems).Error; err != nil {
		logging.ErrorLogger.Error(fmt.Sprintf("Error in ProcessUnmergedMRsByProjectID : %s", err.Error()))
		return err
	}
	if len(mergeRequestItems) == 0 {
		return nil
	}
	for _, mergeRequest := range mergeRequestItems {
		if skipMergeRequestDueToDependencies(mergeRequest) || mergeRequest.ButtonClickUser == "" || skipMergeRequestDueToStatus(mergeRequest) {
			continue
		}
		privateToken, _, err := GetGitlabTokenByUserName(mergeRequest.ButtonClickUser)
		if err != nil {
			logging.ErrorLogger.Error(fmt.Sprintf("Error in ProcessUnmergedMRsByProjectID Failed to get GitLab token : %s", err.Error()))
			continue
		}
		controlReviews, err := findControlReviews(mergeRequest)
		if err != nil {
			logging.ErrorLogger.Error(fmt.Sprintf("Error in ProcessUnmergedMRsByProjectID Failed to find control reviews : %s", err.Error()))
			continue
		}
		gl, err := gitlab.NewClient(privateToken, gitlab.WithBaseURL(libs.Config.Gitlab.Url))
		if err != nil {
			logging.ErrorLogger.Error(fmt.Sprintf("Failed to create GitLab client : %s", err.Error()))
			continue
		}
		gitlabMergeRequest, err := getGitLabMergeRequestDetails(gl, mergeRequest)
		if err != nil {
			logging.ErrorLogger.Error(fmt.Sprintf("Failed to get GitLab merge request details : %s", err.Error()))
			continue
		}
		if shaMismatchResetStatus(mergeRequest, gitlabMergeRequest, controlReviews) {
			continue
		}
		if err := attemptToMergeMR(gl, mergeRequest, controlReviews); err != nil {
			logging.ErrorLogger.Error(fmt.Sprintf("Failed to merge GitLab MR : %s", err.Error()))
			continue
		}
	}
	return nil
}

func skipMergeRequestDueToDependencies(mergeRequest *dmergerequest.MergeRequest) bool {
	for _, dependency := range mergeRequest.Dependencies {
		if dependency.Status != 3 {
			return true
		}
	}
	return false
}

func skipMergeRequestDueToStatus(mergeRequest *dmergerequest.MergeRequest) bool {
	return mergeRequest.Status == -1 || mergeRequest.Status == 3 || mergeRequest.Status == 4
}

func findControlReviews(mergeRequest *dmergerequest.MergeRequest) (*dmergerequescontrolreview.Response, error) {
	controlReviews := &dmergerequescontrolreview.Response{}
	err := easygorm.GetEasyGormDb().Model(dmergerequescontrolreview.Model()).
		Where("merge_request_id = ? and release_project_id = ? and work_package_id = ? and status=0", mergeRequest.ID, mergeRequest.ReleaseProjectID, mergeRequest.WorkPackageID).Find(controlReviews).Error
	return controlReviews, err
}

func getGitLabMergeRequestDetails(gl *gitlab.Client, mergeRequest *dmergerequest.MergeRequest) (*gitlab.MergeRequest, error) {
	gitlabMergeRequest, _, err := gl.MergeRequests.GetMergeRequest(int(mergeRequest.TargetProjectID), int(mergeRequest.MergeRequestIID), &gitlab.GetMergeRequestsOptions{})
	return gitlabMergeRequest, err
}

func shaMismatchResetStatus(mergeRequest *dmergerequest.MergeRequest, gitlabMergeRequest *gitlab.MergeRequest, controlReviews *dmergerequescontrolreview.Response) bool {
	if gitlabMergeRequest.SHA != mergeRequest.MergeRequestSHA {
		if controlReviews.ID > 0 {
			// 审批单关闭
			controlreviewObjects := map[string]interface{}{
				"Status":  3,
				"Comment": "参数异常：MR表单SHA不一致，系统自动关闭",
			}
			controlreviewUP := dmergerequescontrolreview.Response{}
			controlreviewUP.Update(controlReviews.ID, controlreviewObjects)
		}
		go SendMailToReviewUser(*mergeRequest, 2) // 发送邮件通知
		return true
	}
	return false
}

func attemptToMergeMR(gl *gitlab.Client, mergeRequest *dmergerequest.MergeRequest, controlReviews *dmergerequescontrolreview.Response) error {
	sha := mergeRequest.MergeRequestSHA
	_, _, err := gl.MergeRequests.AcceptMergeRequest(int(mergeRequest.TargetProjectID), int(mergeRequest.MergeRequestIID), &gitlab.AcceptMergeRequestOptions{SHA: &sha})
	if err != nil {
		return err
	}
	if controlReviews.ID > 0 {
		// 更新管控MR审核表单状态
		controlreviewObjects := map[string]interface{}{
			"Status":  1,
			"Comment": "项目打开状态开启，自动通过该审批单",
		}
		controlreviewUP := dmergerequescontrolreview.Response{}
		controlreviewUP.Update(controlReviews.ID, controlreviewObjects)
	}
	go SendMailSuccess(*mergeRequest) // 发送邮件通知成功了
	return nil
}

func MergeMergeRequestByGitlabSession(mergeRequestIID int, gitlabSession, csrfToken, PathWithNamespace, sha string) (string, error) {
	// 构造请求地址
	url := fmt.Sprintf("%s/%s/-/merge_requests/%d/merge?real=1", libs.Config.Gitlab.Url, PathWithNamespace, mergeRequestIID)
	// 构造请求体
	requestBody, err := json.Marshal(map[string]interface{}{
		"sha":                         sha,
		"should_remove_source_branch": false,
		"squash":                      false,
	})
	if err != nil {
		return "", err
	}
	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", err
	}
	// 设置请求头
	_gitlabSession := "_gitlab_session=" + gitlabSession
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Cookie", _gitlabSession)
	req.Header.Set("X-Csrf-Token", csrfToken)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	// fmt.Printf("Response Body: %s\n", string(body))

	// 检查状态码
	if resp.StatusCode != 200 {
		logging.ErrorLogger.Error(fmt.Sprintf("request_url:%s,Cookie:%s,Csrf-Token:%s failed to merge merge request: %s +StatusCode:%d", url, _gitlabSession, csrfToken, string(body), resp.StatusCode))
		return "", fmt.Errorf("request_url:%s,Cookie:%s,Csrf-Token:%s failed to merge merge request: %s", url, _gitlabSession, csrfToken, string(body))
	}
	// 解析响应体以验证操作是否成功
	var responseMap map[string]interface{}
	if err := json.Unmarshal(body, &responseMap); err != nil {
		return "", err
	}
	if status, ok := responseMap["status"].(string); !ok || status != "success" {
		logging.ErrorLogger.Error("url:%v, _gitlabSession:%v, csrfToken:%v, merge request failed, response status: %v", url, _gitlabSession, csrfToken, responseMap["status"])
		return "", fmt.Errorf("request_url:%s,Cookie:%s,Csrf-Token:%s  merge request failed, response status: %v", url, _gitlabSession, csrfToken, responseMap["status"])
	}
	return string(body), nil
}

func UpdateButtonClickLog(logEntry LogEntry, buttonclicklogObjects dmergerequesbuttonclicklog.Response) error {
	logEntryJSON, jsonErr := json.Marshal(logEntry)
	if jsonErr != nil {
		logging.ErrorLogger.Errorf("Failed to marshal log entry: %v", jsonErr)
		return jsonErr
	}
	buttonclicklogUpdateObjects := map[string]interface{}{
		"AcceptMergeRequest": string(logEntryJSON),
	}
	fmt.Println(buttonclicklogObjects.ID, buttonclicklogUpdateObjects)
	dmergerequesbuttonclicklog := dmergerequesbuttonclicklog.Response{}
	err := dmergerequesbuttonclicklog.Update(buttonclicklogObjects.ID, buttonclicklogUpdateObjects)
	if err != nil {
		logging.ErrorLogger.Errorf("Error updating buttonclicklog:", err)
		return err
	}
	return nil
}

type GitLabUser struct {
	ID               int           `json:"id"`
	Name             string        `json:"name"`
	Username         string        `json:"username"`
	State            string        `json:"state"`
	AvatarURL        string        `json:"avatar_url"`
	WebURL           string        `json:"web_url"`
	CreatedAt        time.Time     `json:"created_at"`
	Bio              string        `json:"bio"`
	BioHTML          string        `json:"bio_html"`
	Location         interface{}   `json:"location"`
	PublicEmail      string        `json:"public_email"`
	Skype            string        `json:"skype"`
	Linkedin         string        `json:"linkedin"`
	Twitter          string        `json:"twitter"`
	WebsiteURL       string        `json:"website_url"`
	Organization     interface{}   `json:"organization"`
	JobTitle         string        `json:"job_title"`
	Bot              bool          `json:"bot"`
	WorkInformation  interface{}   `json:"work_information"`
	Followers        int           `json:"followers"`
	Following        int           `json:"following"`
	LastSignInAt     time.Time     `json:"last_sign_in_at"`
	ConfirmedAt      time.Time     `json:"confirmed_at"`
	LastActivityOn   string        `json:"last_activity_on"`
	Email            string        `json:"email"`
	ThemeID          int           `json:"theme_id"`
	ColorSchemeID    int           `json:"color_scheme_id"`
	ProjectsLimit    int           `json:"projects_limit"`
	CurrentSignInAt  time.Time     `json:"current_sign_in_at"`
	Identities       []interface{} `json:"identities"`
	CanCreateGroup   bool          `json:"can_create_group"`
	CanCreateProject bool          `json:"can_create_project"`
	TwoFactorEnabled bool          `json:"two_factor_enabled"`
	External         bool          `json:"external"`
	PrivateProfile   bool          `json:"private_profile"`
	IsAdmin          bool          `json:"is_admin"`
	Note             string        `json:"note"`
}

type Merge struct {
	Sha                      string `json:"sha"`
	CommitMessage            string `json:"commit_message"`
	ShouldRemoveSourceBranch bool   `json:"should_remove_source_branch"`
	Squash                   bool   `json:"squash"`
}

type MergeRequestWorkPackage struct {
	models.ModelBase
	Name                        string  `json:"name"`
	ReleaseProjectID            uint    `json:"release_project_id"`
	CodeQuantity                float32 `json:"code_quantity"`
	PortedCodeQuantity          float32 `json:"ported_code_quantity"`
	TemporaryPortedCodeQuantity float32 `json:"temporary_ported_code_quantity"`
	TotalCodeQuantity           float32 `json:"total_code_quantity"`
	OwnerID                     uint    `json:"owner_id"`
	PstlID                      uint    `json:"pstl_id"`
	WorkGroup                   string  `json:"work_group"`
	Requirement                 string  `json:"requirement"`
	MrControlStatus             uint    `json:"mr_control_status"`
}

type LogEntry struct {
	Error   string      `json:"error,omitempty"`
	Status  string      `json:"status,omitempty"`
	Headers http.Header `json:"headers,omitempty"`
}
