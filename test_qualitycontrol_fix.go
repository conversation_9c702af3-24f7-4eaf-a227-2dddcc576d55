package main

import (
	"fmt"
	"log"
	"time"

	"irisAdminApi/application/controllers/openfeishu"
	"irisAdminApi/application/libs"
	"irisAdminApi/application/logging"
)

func main() {
	// 初始化配置
	libs.InitConfig("config.yaml")

	// 日志会自动初始化
	_ = logging.InfoLogger

	fmt.Println("=== 品控系统登录修复测试 ===")

	// 测试Cookie管理器初始化
	fmt.Println("1. 测试Cookie管理器初始化...")
	manager := openfeishu.GetQualityControlCookieManager()
	if manager == nil {
		log.Fatal("Cookie管理器初始化失败")
	}
	fmt.Println("✓ Cookie管理器初始化成功")

	// 测试登录功能
	fmt.Println("2. 测试登录功能...")
	startTime := time.Now()
	err := manager.Login()
	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("✗ 登录失败: %v (耗时: %v)\n", err, duration)
	} else {
		fmt.Printf("✓ 登录成功 (耗时: %v)\n", duration)
	}

	// 测试API调用
	fmt.Println("3. 测试API调用...")
	api := openfeishu.NewQualityControlAPI()
	if api == nil {
		log.Fatal("API实例创建失败")
	}

	// 测试获取品控列表
	listResponse, err := api.GetQualityControlList(1, 10, "")
	if err != nil {
		fmt.Printf("✗ 获取品控列表失败: %v\n", err)
	} else {
		fmt.Printf("✓ 获取品控列表成功，记录数: %d\n", len(listResponse.Rows))
	}

	fmt.Println("=== 测试完成 ===")
}
